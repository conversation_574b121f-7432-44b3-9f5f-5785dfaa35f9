#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

print("🚀 开始测试...")

try:
    import lazyllm
    print("✅ lazyllm 导入成功")
except ImportError as e:
    print(f"❌ lazyllm 导入失败: {e}")
    exit(1)

try:
    from lazyllm.module import OnlineEmbeddingModuleBase
    print("✅ OnlineEmbeddingModuleBase 导入成功")
except ImportError as e:
    print(f"❌ OnlineEmbeddingModuleBase 导入失败: {e}")
    exit(1)

try:
    from typing import Dict, List, Union
    print("✅ typing 模块导入成功")
except ImportError as e:
    print(f"❌ typing 模块导入失败: {e}")

print("🎉 所有基础模块导入成功!")

# 测试 Jina AI 嵌入模块定义
try:
    class JinaEmbeddingModule(OnlineEmbeddingModuleBase):
        """Jina AI 嵌入模块 - 符合 OpenAI 格式"""
        
        def __init__(self, api_key: str, model_name: str = "jina-embeddings-v3", task: str = "text-matching"):
            # Jina AI 的 API 端点
            embed_url = "https://api.jina.ai/v1/embeddings"
            
            super().__init__(
                model_series="openai",  # 使用 openai 格式
                embed_url=embed_url,
                api_key=api_key,
                embed_model_name=model_name
            )
            self.task = task
        
        def _encapsulated_data(self, text: str, **kwargs) -> Dict:
            """封装请求数据为 OpenAI/Jina AI 格式"""
            # 如果是单个文本，转换为列表
            if isinstance(text, str):
                input_texts = [text]
            else:
                input_texts = text if isinstance(text, list) else [text]
                
            json_data = {
                "model": self._embed_model_name,
                "input": input_texts,
                "task": self.task  # Jina AI 特有的参数
            }
            
            # 添加其他参数
            if len(kwargs) > 0:
                json_data.update(kwargs)
                
            return json_data
        
        def _parse_response(self, response: Union[List, Dict]) -> Union[List[List[float]], List[float]]:
            """解析 OpenAI/Jina AI 格式的响应"""
            if isinstance(response, dict) and "data" in response:
                # OpenAI/Jina AI 返回格式: {"data": [{"embedding": [...]}, ...]}
                embeddings = [item["embedding"] for item in response["data"]]
                return embeddings[0] if len(embeddings) == 1 else embeddings
            else:
                raise ValueError(f"无法解析响应格式: {response}")
    
    print("✅ JinaEmbeddingModule 类定义成功")
    
    # 尝试创建实例
    jina_embedding = JinaEmbeddingModule(
        api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
        model_name="jina-embeddings-v3",
        task="text-matching"
    )
    print("✅ JinaEmbeddingModule 实例创建成功")
    print(f"   模型: {jina_embedding._embed_model_name}")
    print(f"   任务: {jina_embedding.task}")
    print(f"   URL: {jina_embedding._embed_url}")
    
except Exception as e:
    print(f"❌ JinaEmbeddingModule 创建失败: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 测试完成!")
print("💡 如果看到这条消息，说明基础配置正确")
print("📝 接下来可以测试实际的嵌入功能")
