import lazyllm  # (1)
import requests
from loguru import logger
import sys
from pathlib import Path

# 配置Loguru日志
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

# 移除默认处理器
logger.remove()

# 添加控制台输出（彩色）
logger.add(
    sys.stderr,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO",
)

# 添加文件输出（详细日志）
logger.add(
    log_dir / "chat_{time:YYYY-MM-DD}.log",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG",
    rotation="1 day",
    retention="7 days",
    compression="zip",
)

# 添加错误日志文件
logger.add(
    log_dir / "error_{time:YYYY-MM-DD}.log",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="ERROR",
    rotation="1 day",
    retention="30 days",
)

# 初始化聊天模块
logger.info("开始初始化聊天模块")
try:
    chat = lazyllm.OnlineChatModule(
        source="doubao",
        model="deepseek-v3-250324",
        base_url="https://ark.cn-beijing.volces.com/api/v3/",
        api_key="5dd64d02-d0b4-48fd-91f0-d4a91f359456",
        stream=True,
    )
    logger.success("聊天模块初始化成功")
    logger.debug(f"模型配置 - source: doubao, model: deepseek-v3-250324")
except Exception as e:
    logger.error(f"聊天模块初始化失败: {e}")
    logger.critical("程序退出")
    exit(1)

logger.info("聊天会话开始")

while True:
    query = input("query(enter 'quit' to exit): ")  # (3)
    if query == "quit":  # (4)
        logger.info("用户选择退出聊天")
        break

    logger.debug(f"用户输入: {query}")

    try:
        logger.info("正在处理用户查询...")
        res = chat.forward(query)  # (5)
        logger.success("查询处理成功")
        logger.debug(f"AI回复长度: {len(str(res))} 字符")
        print(f"answer: {res}")  # (6)
    except requests.exceptions.RequestException as e:
        logger.error(f"网络请求错误: {e}")
        print(f"请求错误: {e}")
        print("可能的原因:")
        print("1. API密钥无效或过期")
        print("2. 模型ID不正确")
        print("3. 网络连接问题")
        print("4. API配额不足")
        logger.warning("建议检查网络连接和API配置")
    except Exception as e:
        logger.error(f"未知错误: {e}")
        logger.exception("详细错误信息:")
        print(f"其他错误: {e}")

logger.info("聊天会话结束")
