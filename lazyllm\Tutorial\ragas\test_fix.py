#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复结果
"""

def test_magic():
    """测试magic库"""
    print("测试magic库...")
    try:
        import magic
        print("✅ magic库导入成功")
        return True
    except ImportError as e:
        print(f"❌ magic库导入失败: {e}")
        return False

def test_core_modules():
    """测试核心模块"""
    print("\n测试核心模块...")
    modules = {
        "langchain_openai": "LangChain OpenAI",
        "jina_embeddings": "Jina Embeddings", 
        "numpy": "NumPy",
        "ragas": "RAGAS"
    }
    
    results = {}
    for module, name in modules.items():
        try:
            __import__(module)
            print(f"✅ {name} - 导入成功")
            results[module] = True
        except ImportError as e:
            print(f"❌ {name} - 导入失败: {e}")
            results[module] = False
        except Exception as e:
            print(f"⚠️ {name} - 其他错误: {e}")
            results[module] = False
    
    return results

def test_document_loading():
    """测试文档加载"""
    print("\n测试文档加载...")
    
    import os
    import glob
    
    if not os.path.exists('Sample_Docs_Markdown'):
        print("❌ Sample_Docs_Markdown 目录不存在")
        return False
    
    md_files = glob.glob(os.path.join('Sample_Docs_Markdown', "*.md"))
    print(f"找到 {len(md_files)} 个markdown文件")
    
    successful_loads = 0
    for file_path in md_files[:5]:  # 只测试前5个文件
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
                # 检查是否包含模板语法
                if '{{' in content or '}}' in content:
                    print(f"⚠️ 跳过模板文件: {os.path.basename(file_path)}")
                    continue
                
                if len(content.strip()) > 50:
                    successful_loads += 1
                    print(f"✅ 成功: {os.path.basename(file_path)}")
                else:
                    print(f"⚠️ 内容过短: {os.path.basename(file_path)}")
                    
        except Exception as e:
            print(f"❌ 失败: {os.path.basename(file_path)} - {e}")
    
    print(f"\n成功加载 {successful_loads} 个文档")
    return successful_loads > 0

def main():
    print("=" * 50)
    print("RAG系统修复结果测试")
    print("=" * 50)
    
    # 测试magic库
    magic_ok = test_magic()
    
    # 测试核心模块
    modules_ok = test_core_modules()
    
    # 测试文档加载
    docs_ok = test_document_loading()
    
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    if magic_ok:
        print("✅ libmagic问题已解决")
    else:
        print("⚠️ libmagic仍有问题，但不影响核心功能")
    
    core_modules_count = sum(modules_ok.values())
    print(f"✅ 核心模块: {core_modules_count}/4 个正常")
    
    if docs_ok:
        print("✅ 文档加载功能正常")
    else:
        print("⚠️ 文档加载需要注意")
    
    print("\n建议:")
    if magic_ok:
        print("1. ✅ libmagic警告应该已经消失")
    else:
        print("1. ⚠️ 如果仍有libmagic警告，可以忽略")
    
    print("2. 🔄 重启Jupyter notebook以应用更改")
    print("3. 📝 使用过滤后的文档避免加载错误")
    
    if core_modules_count == 4:
        print("4. 🎉 RAG系统应该可以正常运行!")
    else:
        print("4. ⚠️ 某些模块有问题，请检查安装")

if __name__ == "__main__":
    main()
