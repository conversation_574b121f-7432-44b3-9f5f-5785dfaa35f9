{"cells": [{"cell_type": "markdown", "id": "165f3178", "metadata": {}, "source": ["# 第4讲：RAG项目工程化入门：从脚本走向模块化与可维护性"]}, {"attachments": {}, "cell_type": "markdown", "id": "7d29bad2", "metadata": {}, "source": [">一个项目如果代码不规范会导致可读性差、维护困难、容易引入 Bug，并降低代码的可扩展性和一致性。在团队协作中，不统一的风格会增加合并冲>突，影响开发效率。此外，代码不规范可能导致测试困难，影响 CI/CD 流程，甚至带来安全隐患。\n", ">\n", ">本教程将带你从零开始，将一个简单的 Python 脚本逐步演化为规范的工程项目，涵盖 Git版本管理、代码规范检查、单元测试、CI/CD、文档管理，以及打包发布等关键环节。\n", "\n", "---\n", "\n", "## 1. 代码管理：从零开始搭建 Git 项目\n", "\n", "### 1.1 什么是`Git`\n", "\n", "* `Git` 是一个​**分布式版本控制系统**​，用于跟踪文件的更改，特别适用于软件开发。它允许多个开发者协作开发代码，同时保留所有更改的历史记录。[详述](https://git-scm.com/video/what-is-git)\n", "\n", "### ​1.2​ 为什么要使用 Git\n", "\n", "* 代码历史可追溯，支持版本回滚\n", "* 支持多人协作，提高团队效率\n", "* 便于分支管理，降低开发冲突\n", "* 可托管到 github gitlab等，方便备份与共享\n", "\n", "### ​1.3 创建一个GitHub项目\n", "\n", "* 首先你要有一个 GitHub 账号，如果没有的话请先 [注册](https://github.com/join)\n", "* 登陆账号，[创建一个项目](https://github.com/new)（New repository）例如：**my-project**\n", "* 只需要输入项目名称 (Repository name) 即可，项目描述（Description）选填。Public 是公开，可以在 GitHub 搜到，Private 是私密项目，只有自己和项目成员能看到。点击 Create repository\n", "\n", "![image.png](4_images/img1.png)\n", "\n", "### 1.4 初始化 Git 仓库并推送到 GitHub\n", "\n", "1.  在 GitHub 上创建一个新的项目仓库（Repository）。\n", "2.  准备好提交代码需要的 token  [创建教程](https://blog.csdn.net/chengwenyang/article/details/120060010)\n", "3.  在本地初始化 Git 仓库并推送代码：\n"]}, {"cell_type": "markdown", "id": "bf6bb3c0", "metadata": {}, "source": ["```bash\n", ">>> mkdir my-project && ​cd​ my-project\n", ">>> echo \"# test\" >> README.md\n", ">>> git init\n", "\n", "# 首次使用需要设置用户名和邮箱\n", ">>> git config --global user.name \"你的用户名\"\n", ">>> git config --global user.email \"你的邮箱\"\n", "\n", ">>> git add README.md\n", ">>> git commit -m \"test\"\n", ">>> git branch -M main\n", ">>> git remote add origin <your-repo-url>\n", ">>> git push -u origin main\n", "```"]}, {"cell_type": "markdown", "id": "07acbead", "metadata": {}, "source": ["\n", "\n", "4.  输入用户名及token 即可推到远程仓库\n", "\n"]}, {"cell_type": "markdown", "id": "f1f7a11b", "metadata": {}, "source": ["```bash\n", ">>> git push -u origin main\n", "Username for 'https://github.com': lwj-st\n", "Password for 'https://<EMAIL>': \n", "Enumerating objects: 3, done.\n", "Counting objects: 100% (3/3), done.\n", "Writing objects: 100% (3/3), 223 bytes | 223.00 KiB/s, done.\n", "Total 3 (delta 0), reused 0 (delta 0), pack-reused 0\n", "To https://github.com/lwj-st/my-project.git\n", " * [new branch]      main -> main\n", "Branch 'main' set up to track remote branch 'main' from 'origin'.\n", "```"]}, {"cell_type": "markdown", "id": "05dd6ea1", "metadata": {}, "source": ["\n", "\n", "### ​1.5​ 添加基本的工程文件\n", "\n", "* `README.md`介绍项目用途，示例：\n", "\n", "```bash\n", "# my-project\n", "\n", "## 介绍\n", "这是一个示例 Python 项目，支持自动化测试、Docker 部署，并符合 PEP 8 代码规范。\n", "\n", "## 安装\n", "pip install my-project\n", "```"]}, {"cell_type": "markdown", "id": "9c850af9", "metadata": {}, "source": ["\n", "\n", "* `.gitignore`忽略不需要提交的文件，如编译生成的二进制文件、日志文件等 [规则](https://monsterhxw.github.io/posts/git-gitignore-tutorials/)  示例：\n", "\n"]}, {"cell_type": "markdown", "id": "b0c5283e", "metadata": {}, "source": ["```text\n", "        __pycache__\n", "        *.pyc\n", "        test/\n", "        dist/\n", "        tmp/\n", "        .vscode\n", "        build\n", "        *.lock\n", "        *.db\n", "```"]}, {"cell_type": "markdown", "id": "de30422b", "metadata": {}, "source": ["\n", "\n", "* `requirements.txt`记录项目依赖  示例：\n", "\n"]}, {"cell_type": "markdown", "id": "56c4ff87", "metadata": {}, "source": ["```text\n", "        lazyllm\n", "```"]}, {"cell_type": "markdown", "id": "a03119b9", "metadata": {}, "source": ["\n", "\n", "* `Dockerfile`：镜像构建文件 📃 创建并推送镜像\n", "* `setup.py`：打包与发布文件 📃 使用 setuptools 进行打包\n", "\n", "<div style=\"text-align:center; margin:20px 0;\">\n", "  <video controls style=\"width:900px; max-width:100%; height:auto; border:1px solid #ccc; border-radius:8px; box-shadow:0 4px 8px rgba(0,0,0,0.1);\">\n", "    <source src=\"./4_videos/1.mp4\" type=\"video/mp4\" />\n", "    Your browser does not support the video tag.\n", "  </video>\n", "</div>\n", "\n", "### 1.6 添加主项目文件\n", "\n", "项目代码以 【第2讲：10分钟上手一个最小可用RAG系统】为例，添加主代码文件\n", "\n", "* `my_project/retriever.py`\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "1530e2b7", "metadata": {}, "outputs": [], "source": ["from lazyllm import Retriever, Document\n", "\n", "def create_retriever(path: str, query: str):\n", "    \"\"\"\n", "    创建并执行检索\n", "    \n", "    Args:\n", "        path (str): 文档的绝对路径\n", "        query (str): 查询语句\n", "        \n", "    Returns:\n", "        list: 检索结果\n", "    \"\"\"\n", "    doc = Document(path)\n", "    retriever = Retriever(doc, group_name=\"CoarseChunk\", similarity=\"bm25_chinese\", topk=3)\n", "    return retriever(query)"]}, {"cell_type": "markdown", "id": "71e2c164", "metadata": {}, "source": ["\n", "\n", "* `my_project/__init__.py`\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "12b12612", "metadata": {}, "outputs": [], "source": ["from .retriever import create_retriever\n", "\n", "__version__ = '0.1.0'\n", "__all__ = ['create_retriever']"]}, {"cell_type": "markdown", "id": "c614622b", "metadata": {}, "source": ["\n", "\n", "---\n", "\n", "## 2. 版本管理和分支策略\n", "\n", "良好的分支管理策略有助于多人协作和稳定版本发布。\n", "\n", "### 2.1 常见分支命名\n", "\n", "* **​主分支​**（​`main`）​：始终保持稳定可发布状态\n", "* **​开发分支​**（​​`dev`​）​：用于日常开发\n", "* **​功能分支​**（`feature/*`​）​：用于开发新功能，完成后合并回 `dev`\n", "* **​修复分支​**（`hotfix/*`​​）​：用于紧急修复生产环境 bug\n", "\n", "### 2.2 Git 分支管理操作示例\n", "\n"]}, {"cell_type": "markdown", "id": "ab43ca52", "metadata": {}, "source": ["```bash\n", "# 创建并切换到开发分支\n", ">>> git checkout -b dev\n", "\n", "# 在dev分支上新创建功能分支\n", ">>> git checkout -b feature/new-feature\n", "\n", "...修改代码...\n", "\n", ">>> git add .\n", ">>> git commit -m \"Add new feature\"\n", ">>> git push origin feature/new-feature\n", "\n", "# 开发完成后合并回 dev\n", ">>> git checkout dev\n", "\n", ">>> git merge feature/new-feature\n", ">>> git push origin dev\n", "```"]}, {"cell_type": "markdown", "id": "a61ec68c", "metadata": {}, "source": ["\n", "\n", "### 2.3 在github上提交pull request\n", "\n", "* 当改好的代码提到远程仓库后就可以提交pull request了\n", "* 进入 GitHub 仓库主页，点击 \"Pull Requests\" -> \"New Pull Request\"\n", "* 如果是同一仓库的Pull Requests 选择对应的 **base （目标分支）** 和 **compare （比对分支）即可**\n", "* 如果是fork仓库的Pull Requests 则有**​​ base repository （目标仓库）​**和 **​base （目标分支）​**默认是fork的源仓库信息，**​head repository （源仓库）​**和**​​ compare （比对分支）​**默认是自己仓库的信息\n", "* 添加 PR 说明，点击 \"Create Pull Request\"\n", "\n", "### 2.4 解决冲突\n", "\n", "当多个开发者同时修改同一部分代码并尝试合并时，Git 可能会提示**​ conflict（代码冲突）**\n", "\n", "1.  同分支冲突解决\n", "\n", "* 当提交代码Git 检测到冲突时，它会输出类似以下信息：\n", "\n"]}, {"cell_type": "markdown", "id": "8c837452", "metadata": {}, "source": ["```bash\n", "To https://github.com/lwj-st/my-project.git\n", " ! [rejected]        dev -> dev (fetch first)\n", "error: failed to push some refs to 'https://github.com/lwj-st/my-project.git'\n", "hint: Updates were rejected because the remote contains work that you do\n", "hint: not have locally. This is usually caused by another repository pushing\n", "hint: to the same ref. You may want to first integrate the remote changes\n", "hint: (e.g., 'git pull ...') before pushing again.\n", "hint: See the 'Note about fast-forwards' in 'git push --help' for details.\n", "```"]}, {"cell_type": "markdown", "id": "f63ca6f8", "metadata": {}, "source": ["\n", "\n", "* 执行`git pull` merge远程分支\n", "\n"]}, {"cell_type": "markdown", "id": "11b61d3e", "metadata": {}, "source": ["```bash\n", "CONFLICT (content): Merge conflict in requirements.txt\n", "Automatic merge failed; fix conflicts and then commit the result.\n", "```"]}, {"cell_type": "markdown", "id": "ff166239", "metadata": {}, "source": ["\n", "\n", "* 此时，你可以运行`git status`查看哪些文件存在冲突，输出示例：\n", "\n"]}, {"cell_type": "markdown", "id": "461cc2f2", "metadata": {}, "source": ["```bash\n", ">>> git status\n", "On branch dev\n", "Your branch and 'origin/dev' have diverged,\n", "and have 1 and 1 different commits each, respectively.\n", "  (use \"git pull\" to merge the remote branch into yours)\n", "\n", "You have unmerged paths.\n", "  (fix conflicts and run \"git commit\")\n", "  (use \"git merge --abort\" to abort the merge)\n", "\n", "Unmerged paths:\n", "  (use \"git add <file>...\" to mark resolution)\n", "        both modified:   requirements.txt\n", "\n", "no changes added to commit (use \"git add\" and/or \"git commit -a\")\n", "```"]}, {"cell_type": "markdown", "id": "0d3d1d6a", "metadata": {}, "source": ["\n", "\n", "* 直接修改文件，保留正确的代码然后添加并提交：\n", "\n"]}, {"cell_type": "markdown", "id": "9edf9f0f", "metadata": {}, "source": ["```bash\n", ">>> git add requirements.txt\n", ">>> git commit -m \"解决冲突\"\n", ">>> git push\n", "```"]}, {"attachments": {}, "cell_type": "markdown", "id": "780adfe5", "metadata": {}, "source": ["\n", "\n", "<div style=\"text-align:center; margin:20px 0;\">\n", "  <video controls style=\"width:900px; max-width:100%; height:auto; border:1px solid #ccc; border-radius:8px; box-shadow:0 4px 8px rgba(0,0,0,0.1);\">\n", "    <source src=\"./4_videos/2.mp4\" type=\"video/mp4\" />\n", "    Your browser does not support the video tag.\n", "  </video>\n", "</div>\n", "\n", "---\n", "\n", "2.  PR冲突解决\n", "\n", "* 如果PR冲突则会在页面出现 **​This branch has conflicts that must be resolved（这个分支有必须解决的冲突）​**如下：\n", "\n", "![image.png](4_images/img3.png)\n", "\n", "* 页面修改，直接点击 **Resolve conflicts （解决冲突）**\n", "* 编辑冲突文件保留需要的内容后点击 标记为**​​ Mark as resolved（已解决）​**即可\n", "* Git 会在有冲突的文件中标记冲突部分，格式如下：\n", "\n", "![image-2.png](4_images/img2.png)\n"]}, {"cell_type": "markdown", "id": "76d02da6", "metadata": {}, "source": ["```bash\n", "<<<<<<< dev\n", "xxxx \n", "=======\n", "xxxx \n", ">>>>>>> main\n", "```"]}, {"cell_type": "markdown", "id": "6ef1b784", "metadata": {}, "source": ["\n", "\n", "* `<<<<<<< dev ` 表示 **合并进来的 dev** 的代码\n", "* `=======` 是分隔符\n", "* `>>>>>>> main`  表示 **当前分支（main）** 的代码\n", "\n", "<div style=\"text-align:center; margin:20px 0;\">\n", "  <video controls style=\"width:900px; max-width:100%; height:auto; border:1px solid #ccc; border-radius:8px; box-shadow:0 4px 8px rgba(0,0,0,0.1);\">\n", "    <source src=\"./4_videos/3.mp4\" type=\"video/mp4\" />\n", "    Your browser does not support the video tag.\n", "  </video>\n", "</div>\n", "\n", "---\n", "\n", "## 3. 代码规范与自动检查\n", "\n", "为了保证代码质量，我们需要遵循统一的编码规范。\n", "\n", "### 3.1 常用代码格式化与风格检查\n", "\n", "1.  ​`black`​：自动格式化代码\n", "\n", "* 安装 `black`\n", "\n"]}, {"cell_type": "markdown", "id": "aedbe4c6", "metadata": {}, "source": ["```bash\n", ">>> pip install black\n", "```"]}, {"cell_type": "markdown", "id": "5fe2e793", "metadata": {}, "source": ["\n", "\n", "* 格式化整个项目 `black .`\n", "\n"]}, {"cell_type": "markdown", "id": "ef7219e3", "metadata": {}, "source": ["```bash\n", ">>> black .\n", "reformatted /root/my_project/__init__.py\n", "reformatted /root/my_project/retriever.py\n", "\n", "All done! ✨ 🍰 ✨\n", "2 files reformatted.\n", "```"]}, {"cell_type": "markdown", "id": "36f77ec8", "metadata": {}, "source": ["\n", "\n", "* 特点\n", "  * ​**强制代码格式化**​（不能自定义样式，减少团队争议）\n", "  * ​**默认 88 字符换行**​（可调整）\n", "  * ​**自动调整引号**​（优先使用双引号）\n", "  * **对`if-else`​**、列表等结构进行优化**\n", "\n", "2.  ​`flake8`​：代码风格检查\n", "\n", "* 安装 `flake8`\n", "\n"]}, {"cell_type": "markdown", "id": "13db19e8", "metadata": {}, "source": ["```bash\n", ">>> pip install flake8\n", "```"]}, {"cell_type": "markdown", "id": "d42f3279", "metadata": {}, "source": ["\n", "\n", "* 检查整个项目 `flake8 .` 有问题的话会有日志提示，没日志信息就是最好的信息\n", "\n"]}, {"cell_type": "markdown", "id": "17d6c997", "metadata": {}, "source": ["```bash\n", ">>> flake8 .\n", "```"]}, {"cell_type": "markdown", "id": "1f5341df", "metadata": {}, "source": ["\n", "\n", "* 特点\n", "  * ​**代码风格检查**​：基于 PEP 8 规范，检测缩进、命名、行长度等问题。\n", "  * ​**语法错误检测**​：发现未定义变量、语法错误等潜在 Bug。\n", "  * ​**复杂性分析**​：通过 McCabe 复杂度检查，提示代码是否过于复杂。\n", "  * ​**插件扩展性**​：支持第三方插件，可扩展检查规则（如类型检查）。\n", "\n", "3.  ​`pre-commit`​：在提交代码前自动运行检查\n", "\n", "* 安装`pre-commit`\n", "\n"]}, {"cell_type": "markdown", "id": "34495171", "metadata": {}, "source": ["```bash\n", ">>> pip install pre-commit\n", "```"]}, {"cell_type": "markdown", "id": "fb6d5451", "metadata": {}, "source": ["\n", "\n", "* 初始化`pre-commit`\n", "\n"]}, {"cell_type": "markdown", "id": "9dee61e5", "metadata": {}, "source": ["```bash\n", ">>> pre-commit install\n", "```"]}, {"cell_type": "markdown", "id": "669b4b24", "metadata": {}, "source": ["\n", "\n", "* 在 `.pre-commit-config.yaml` 中添加规则：\n", "\n"]}, {"cell_type": "markdown", "id": "f1354860", "metadata": {}, "source": ["```yaml\n", "repos:\n", "  - repo: https://github.com/psf/black\n", "    rev: 23.1.0\n", "    hooks:\n", "      - id: black\n", "\n", "  - repo: https://github.com/pycqa/flake8\n", "    rev: 6.0.0\n", "    hooks:\n", "      - id: flake8\n", "```"]}, {"cell_type": "markdown", "id": "a97ebee1", "metadata": {}, "source": ["\n", "\n", "---\n", "\n", "## 4. 单元测试与代码质量保障\n", "\n", "### 4.1 为什么要写单元测试\n", "\n", "* 可以确保改动后仍然保持正确性，提高代码质量\n", "* 提高代码的可维护性，降低维护成本\n", "* 单元测试可以发现 Bug，避免意外改动影响已有功能，提高开发效率\n", "* 单元测试可以作为文档，帮助团队理解代码\n", "\n", "---\n", "\n", "### 4.2 使用 ​`pytest`​ 编写测试\n", "\n", ">Pytest 是 Python 语言中最流行的测试框架之一，它可以帮助我们自动化测试代码，确保代码按照预期运行，减少人工测试的工作量。\n", "\n", "1.  测试用例规则\n", "\n", "* 文件名 必须以 `test_` 开头或 `_test.py` 结尾。\n", "* 类名 必须以 `Test` 开头，并且不能有`init`方法。\n", "* 函数/方法 必须以 `test_` 开头。\n", "* 可通过 `pytest.ini` 自定义规则。\n", "\n", "2.  安装 `pytest`：\n", "\n"]}, {"cell_type": "markdown", "id": "eb8d1826", "metadata": {}, "source": ["```bash\n", ">>> pip install pytest\n", "```"]}, {"cell_type": "markdown", "id": "3bea3d02", "metadata": {}, "source": ["\n", "\n", "3.  创建 `tests/test_retriever.py`：\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2ee33402", "metadata": {}, "outputs": [], "source": ["import pytest\n", "from my_project import create_retriever\n", "\n", "# 测试文档路径\n", "TEST_PATH = \"./data_kb\"\n", "\n", "def test_retriever_contains_keyword():\n", "    test_query = \"为我介绍一下2008年北京奥运会\"\n", "    expected_keyword = \"奥运比赛\"\n", "    \n", "    results = create_retriever(TEST_PATH, test_query)\n", "    top_content = results[0].get_content() if results else \"\"\n", "    \n", "    assert expected_keyword in top_content, f\"检索结果中未找到关键词 '{expected_keyword}'\"\n", "\n", "def test_retriever_empty_query():\n", "    results = create_retriever(TEST_PATH, \"\")\n", "    assert isinstance(results, list), \"结果应该是列表类型\""]}, {"cell_type": "markdown", "id": "9150eb43", "metadata": {}, "source": ["\n", "\n", "4.  运行测试：（提前准备好测试数据 ./data\\_kb）\n", "\n"]}, {"cell_type": "markdown", "id": "09096309", "metadata": {}, "source": ["```bash\n", ">>> export PYTHONPATH=${PWD}:$PYTHONPATH\n", ">>> pytest --disable-warnings tests/test_retriever.py \n", "==================================== test session starts ====================================\n", "platform linux -- Python 3.10.9, pytest-8.3.3, pluggy-1.5.0\n", "rootdir: /root\n", "plugins: anyio-4.4.0, hydra-core-1.3.2\n", "collected 2 items                                                                                                                                                                                        \n", "\n", "tests/test_retriever.py ..                                                                   [100%]\n", "\n", "===================================== 2 passed in 0.01s =====================================\n", "```"]}, {"cell_type": "markdown", "id": "7cd8cec7", "metadata": {}, "source": ["\n", "* --disable-warnings  忽略警告日志（只会打印错误日志）\n", "\n", "---\n", "\n", "### 4.3 pytest中常用的标记​（扩展）\n", "\n", "1.  pytest 提供了 `markers`（标记） 机制，可以用来分类测试、控制测试执行、参数化测试等。以下是一些常见的 pytest 标记及其用途。\n", "2.  示例 `tests/test_server.py`\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "076a6b6e", "metadata": {}, "outputs": [], "source": ["# content of test_server.py\n", "import pytest\n", "\n", "@pytest.mark.webtest\n", "def test_send_http():\n", "    pass  # perform some webtest test for your app\n", "    \n", "@pytest.mark.device(serial=\"123\")\n", "def test_something_quick():\n", "    pass\n", "    \n", "@pytest.mark.device(serial=\"abc\")\n", "def test_another():\n", "    pass\n", "    \n", "class TestClass:\n", "    def test_method(self):\n", "        pass"]}, {"cell_type": "markdown", "id": "e0acdb13", "metadata": {}, "source": ["\n", "\n", "3.  然后，您可以将测试运行限制为仅运行标有 的测试`webtest`\n", "\n"]}, {"cell_type": "markdown", "id": "9301d01a", "metadata": {}, "source": ["```bash\n", ">>>  pytest -v -m webtest\n", "==================================== test session starts ====================================\n", "platform linux -- Python 3.10.9, pytest-8.3.3, pluggy-1.5.0 -- /opt/miniconda3/envs/lazyllm/bin/python\n", "cachedir: .pytest_cache\n", "rootdir: /root/my-project\n", "plugins: anyio-4.4.0, hydra-core-1.3.2\n", "collected 4 items / 3 deselected / 1 selected\n", "\n", "tests/test_server.py::test_send_http PASSED                                           [100%]\n", "======================== 1 passed, 3 deselected, 3 warnings in 0.01s ========================\n", "```"]}, {"cell_type": "markdown", "id": "3c52ac62", "metadata": {}, "source": ["\n", "\n", "4.  此外，您可以限制测试运行，仅运行与一个或多个标记关键字参数匹配的测试，例如，仅运行标有`device`和特定的测试`serial=\"123\"`：\n", "\n"]}, {"cell_type": "markdown", "id": "558f7238", "metadata": {}, "source": ["```bash\n", ">>>  pytest -v -m \"device(serial='123')\"\n", "==================================== test session starts ====================================\n", "platform linux -- Python 3.10.9, pytest-8.3.3, pluggy-1.5.0 -- /opt/miniconda3/envs/lazyllm/bin/python\n", "cachedir: .pytest_cache\n", "rootdir: /root/my-project\n", "plugins: anyio-4.4.0, hydra-core-1.3.2\n", "collected 4 items / 3 deselected / 1 selected\n", "\n", "tests/test_server.py::test_something_quick PASSED                                     [100%]\n", "======================== 1 passed, 3 deselected, 3 warnings in 0.03s ========================\n", "```"]}, {"cell_type": "markdown", "id": "8699c556", "metadata": {}, "source": ["5.  更多信息可参考 [markers](https://docs.pytest.org/en/stable/example/markers.html)\n", "\n", "---\n", "\n", "### 4.4 测试覆盖率\n", "\n", "* 在持续集成（CI）中，测试覆盖率是衡量代码质量的重要指标。使用 `pytest` 结合 `pytest-cov` 插件，可以生成详细的测试覆盖率报告，帮助开发者分析哪些代码未被测试。\n", "* 执行测试，添加覆盖率统计：\n"]}, {"cell_type": "markdown", "id": "89ec9a44", "metadata": {}, "source": ["```bash\n", ">>> export PYTHONPATH=${PWD}:$PYTHONPATH\n", ">>> pytest --cov=my_project --cov-append --cov-report=html\n", "```"]}, {"attachments": {}, "cell_type": "markdown", "id": "070cbc5b", "metadata": {}, "source": ["\n", "\n", "* 测试完成后，可以在 **htmlcov ​**目录 查看HTML 报告。\n", "* `pytest --cov=my-project` 统计 `my-project` 目录下的代码覆盖率。\n", "* `--cov-append` 确保多次运行时，覆盖率不会被重置。\n", "* `--cov-report=html` 生成可视化 HTML 报告，帮助分析测试覆盖率。\n", "* 详细可参考 [pytest-cov](https://pytest-cov.readthedocs.io/en/latest/)\n", "\n", "![image.png](4_images/img4.png)\n", "---\n", "\n", "## 5. 持续集成（CI）：让测试自动化\n", "\n", "### 5.1 为什么要CI\n", "\n", "1. 传统项目开发测试会遇到下面等问题\n", "   - 传统手动运行测试，可能会忘记或执行不完整。\n", "   - 多人协作时，每个人的测试环境可能不同，导致\\*\\*“在我电脑上能跑”\\*\\*的问题。\n", "   - 如果代码合并后才手动测试，可能要到后期才发现 Bug，修复成本更高。\n", "   - 代码变更较多时，手动测试可能遗漏边界情况。\n", "   - 不同开发者的代码风格不同，可能导致代码风格混乱。\n", "2. 使用GitHub Actions CI有下面等优点\n", "   - 每次**​提交（push）​**或**​拉取请求（pull request）​**时，自动运行测试，避免问题进入主分支。\n", "   - 开发者能在第一时间知道代码是否通过所有测试。\n", "   - 代码测试在标准化的GitHub Runner环境运行，不受开发者本地环境影响。\n", "   - 可以并行运行多个测试，加快反馈速度。\n", "   - 使用 ​`prettier`​、`black`​、`flake8`​、`eslint` 等工具，自动检查代码格式，保持代码风格一致。\n", "   - <PERSON>ith<PERSON> 提供免费的runner使用，可以白嫖\n", "\n", "---\n", "\n", "### ​5.2 配置CI\n", "\n", "* 添加 `.github/workflows/test.yml` ：\n", "\n"]}, {"cell_type": "markdown", "id": "24be2fbe", "metadata": {}, "source": ["```yaml\n", "name: Run Tests\n", "on: [push, pull_request]\n", "jobs:\n", "  test:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - uses: actions/checkout@v3\n", "      - name: Set up Python\n", "        uses: actions/setup-python@v3\n", "        with:\n", "          python-version: \"3.10\"\n", "      - name: Install dependencies\n", "        run: pip install -r requirements.txt\n", "      - name: Run tests\n", "        run: |\n", "          export PYTHONPATH=${PWD}:$PYTHONPATH \n", "          pytest\n", "```"]}, {"attachments": {}, "cell_type": "markdown", "id": "521fc6ac", "metadata": {}, "source": ["\n", "* 可实现每次提交pr或合入分支时进行测试\n", "\n", "![image.png](4_images/img5.png)\n", "\n", "---\n", "\n", "## 6. 代码文档管理：用 MkDocs 搭建文档系统\n", "\n", "### 6.1 为什么要用MkDocs\n", "\n", "1. 在项目开发过程中，文档管理常常遇到以下问题：\n", "   - 不同开发者可能使用 **Word、Markdown、PDF、Wiki** 等不同格式，导致难以维护。\n", "   - 许多文档存储在本地，缺少版本控制，难以追踪更改历史。\n", "   - 需要手动更新编译，过程繁琐。\n", "2. 使用MkDocs 可以很好的这些问题\n", "   - 使用 Markdown 编写，简单易读，统一格式，降低学习成本。\n", "   - Git 版本控制，文档与代码一起管理，随代码更新而更新。\n", "   - 结合 GitHub Actions 或 Read the Docs，提交代码后自动生成最新文档，无需手动更新。\n", "\n", "---\n", "\n", "### ​6.2​ 使用 MkDocs 生成文档\n", "\n", "1.  安装依赖包，有关详细信息，请参阅[安装指南](https://www.mkdocs.org/user-guide/installation/)。\n", "\n"]}, {"cell_type": "markdown", "id": "febc58e5", "metadata": {}, "source": ["```bash\n", ">>> pip install mkdocs\n", "```"]}, {"cell_type": "markdown", "id": "e320cd75", "metadata": {}, "source": ["\n", "\n", "2.  初始化 MkDocs：\n", "\n"]}, {"cell_type": "markdown", "id": "5b089fe3", "metadata": {}, "source": ["```bash\n", ">>> mkdocs new my-project\n", ">>> cd my-project\n", "```"]}, {"cell_type": "markdown", "id": "b96bd1ad", "metadata": {}, "source": ["\n", "\n", "3.  创建文件目录如下：有一个配置文件`mkdocs.yml`，以及一个名为  `docs` 的文件夹，其中包含您的文档源文件（是[docs\\_dir](https://www.mkdocs.org/user-guide/configuration/#docs_dir) 配置设置的默认值）。目前该 `docs` 文件夹仅包含一个名为`index.md` 的文档页面。\n", "\n"]}, {"cell_type": "markdown", "id": "f36e1e38", "metadata": {}, "source": ["```yaml\n", "my-project/\n", "├── docs\n", "│   └── index.md\n", "└── mkdocs.yml\n", "```"]}, {"cell_type": "markdown", "id": "28104825", "metadata": {}, "source": ["\n", "\n", "4.  查看项目\n", "\n", "* 然后通过运行以下命令启动服务器`mkdocs serve`\n", "  * 端口被占用时可以用 `-a` 指定端口 `mkdocs serve -a 0.0.0.0:8008`\n", "  \n"]}, {"cell_type": "markdown", "id": "c97caa98", "metadata": {}, "source": ["```bash\n", "  $ mkdocs serve\n", "  INFO    -  Building documentation...\n", "  INFO    -  Cleaning site directory\n", "  INFO    -  Documentation built in 0.22 seconds\n", "  INFO    -  [15:50:43] Watching paths for changes: 'docs', 'mkdocs.yml'\n", "  INFO    -  [15:50:43] Serving on http://127.0.0.1:8000/\n", "```"]}, {"attachments": {}, "cell_type": "markdown", "id": "060ddbd6", "metadata": {}, "source": ["* 在浏览器中打开[http://127.0.0.1:8000/](http://127.0.0.1:8000/)，您将看到显示的默认主页：\n", "* 详细配置参考：https://www.mkdocs.org/\n", "\n", "![image.png](4_images/img6.png)\n", "\n", "---\n", "\n", "### ​6.3 托管到 Read the Docs\n", "\n", "1.  为什么选择**Read the Docs ？**\n", "\n", "在开发项目时，良好的 **文档** 是不可或缺的。相比把文档散落在本地文件、Markdown 文件或 Wiki 页面上，**Read the Docs** 提供了一个高效的在线文档托管和自动构建平台，特别适合 **开源项目** 和 ​**持续更新的技术文档**​。\n", "\n", "以下是托管到 Read the Docs 的几个核心优势：\n", "\n", "* ​**自动构建**​：推送代码到 GitHub，Read the Docs 就会 自动构建并更新文档。\n", "* ​**版本管理**​：支持多个文档版本，可以让用户查看不同版本的文档（比如 `latest`、`stable`、`v1.0`）\n", "* **在线搜索： ​**提供在线搜索功能，可以快速查找内容。\n", "* ​**免费托管**​：完全免费，不需要额外购买服务器或域名。且自带​**https 安全访问**​，无需配置 SSL 证书。\n", "\n", "2.  添加`.readthedocs.yaml` 文件 [配置说明](https://docs.readthedocs.com/platform/stable/config-file/v2.html)\n", "\n"]}, {"cell_type": "markdown", "id": "d24885e9", "metadata": {}, "source": ["```yaml\n", "# .readthedocs.yaml\n", "# Read the Docs 配置文件\n", "\n", "# 必需的版本字段\n", "version: 2\n", "\n", "# 设置构建环境\n", "build:\n", "  os: ubuntu-24.04\n", "  tools:\n", "    python: \"3.10\"  # MkDocs 需要 Python\n", "\n", "# 配置 MkDocs\n", "mkdocs:\n", "  configuration: mkdocs.yml  # 默认的 MkDocs 配置文件\n", "\n", "# 可选：指定 Python 依赖（如果有）\n", "#python:\n", "#  install:\n", "#    - requirements: requirements.txt  # 如果你有依赖文件\n", "```"]}, {"cell_type": "markdown", "id": "f7b232ca", "metadata": {}, "source": ["\n", "\n", "3.  注册 [Read the Docs](https://readthedocs.org/) 账号 （选择github自动登录）\n", "4.  在 Read the Docs 主页，点击 `导入一个项目`（​**Import a Project​**）​。\n", "5.  选择需要托管的 GitHub 仓库，点击 **➕** 继续。\n", "6.  设置\n", "   1.  名称 ：默认仓库名\n", "   2.  默认分支 ：选主分支\n", "   3.  语言：该项目的文档所呈现的语言\n", "7.  点击 ​`下一页`​，系统会开始自动构建文档。\n", "8.  Read the Docs 会自动构建并托管文档\n", "9.  查看文档 [https://<your-project>.readthedocs.io/](https://your-project.readthedocs.io/)   <your-project> 换成自己的项目名\n", "\n", "<div style=\"text-align:center; margin:20px 0;\">\n", "  <video controls style=\"width:900px; max-width:100%; height:auto; border:1px solid #ccc; border-radius:8px; box-shadow:0 4px 8px rgba(0,0,0,0.1);\">\n", "    <source src=\"./4_videos/4.mp4\" type=\"video/mp4\" />\n", "    Your browser does not support the video tag.\n", "  </video>\n", "</div>\n", "\n", "---\n", "\n", "## 7. 制品\n", "\n", "### 7.1 为什么要做制品\n", "\n", "在 Python 项目开发过程中，除了编写代码，我们通常还需要将项目打包成 **可发布、可安装、可复现** 的格式，这些被称为 ​**制品（Artifacts）**​。制品可以是 ​**Python 包（wheel、sdist）、Docker 镜像、二进制文件、可执行程序**​。\n", "\n", "项目制作制品的核心意义：\n", "\n", "* ​**便于发布与分发**​：如果没有制品，用户需要自己下载源码、安装依赖、配置环境，过程繁琐且容易出错。\n", "* ​**确保环境一致性**​：不同环境（开发、测试、生产）可能依赖不同版本的软件，导致程序行为不一致。\n", "* ​**便于测试与回滚**​：有对应的制品方便直接选用对应的版本的制品使用，如果没有制品，每次测试都需要重新搭建环境，回滚版本困难。\n", "\n", "### 7.2 whl包制品\n", "\n", "#### 7.2.1 为什么要做whl包制品\n", "\n", "`.whl`（Wheel）是一种 ​**Python 包的二进制分发格式**​，相比源码安装更高效，广泛用于发布和部署 Python 项目。\n", "\n", "* **提高安装速度: ​**打成whl包可以直接通过 `pip install my-project.whl` 比源码安装更快\n", "* **​行业共识：​**现在python制品通常都是做成whl包，并上传官方pypi仓库，方便更好的传播与使用\n", "\n", "---\n", "\n", "#### ​7.2.2​ 使用 `setuptools​` ​ 进行打包\n", "\n", "在 `setup.py` 中定义：\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "3d86d7b8", "metadata": {}, "outputs": [], "source": ["from setuptools import setup, find_packages\n", "\n", "setup(\n", "    name=\"my_project\",\n", "    version=\"0.1.0\",\n", "    packages=find_packages(),\n", "    install_requires=[\n", "        \"lazyllm\", \n", "    ],\n", "    author=\"Your Name\",\n", "    author_email=\"<EMAIL>\",\n", "    description=\"A simple RAG retriever package\",\n", "    long_description=open(\"README.md\").read(),\n", "    long_description_content_type=\"text/markdown\",\n", "    python_requires=\">=3.6\",\n", ")"]}, {"cell_type": "markdown", "id": "da5cff75", "metadata": {}, "source": ["\n", "\n", "生成 `.whl` 包：\n", "\n"]}, {"cell_type": "markdown", "id": "ec856f3c", "metadata": {}, "source": ["```bash\n", ">>> python setup.py bdist_wheel\n", "running bdist_wheel\n", "running build\n", "running build_py\n", "creating build\n", "creating build/lib\n", "creating build/lib/my_project\n", "\n", "....\n", "\n", "adding 'my_project-0.1.0.dist-info/WHEEL'\n", "adding 'my_project-0.1.0.dist-info/top_level.txt'\n", "adding 'my_project-0.1.0.dist-info/RECORD'\n", "removing build/bdist.linux-x86_64/wheel\n", ">>> ls dist/\n", "my_project-0.1.0-py3-none-any.whl\n", "```"]}, {"cell_type": "markdown", "id": "d531b304", "metadata": {}, "source": ["\n", "\n", "---\n", "\n", "#### ​7.2.3 注册pypi账号\n", "\n", "1. 访问 [PyPI 官网](https://pypi.org/) 创建账号并登录。\n", "2. 点击右上角 \"[Account settings](https://pypi.org/manage/account/)\" 进入设置页面。\n", "3. 在 \"API tokens\"（API 令牌） 部分，点击 \"Add API token\"（添加 API 令牌）。\n", "4. 配置 Token：\n", "   - 名称（Name）：例如 `INDEX_PYPI_TOKEN`\n", "   - 作用范围（<PERSON><PERSON>）：\n", "      * Entire account（整个账户）：允许管理所有 PyPI 项目（不推荐）\n", "      * Specific project（指定项目）：建议选择你的项目名称（更安全）\n", "5. 点击 Create token（创建令牌）。\n", "6. 复制生成的 API Token（仅显示一次，注意：不要泄露此令牌！）\n", "\n", "---\n", "\n", "#### 7.2.4 制作并推送whl包\n", "\n", "7.  本地添加  `~/.pypirc` 文件\n", "\n"]}, {"cell_type": "markdown", "id": "770196ae", "metadata": {}, "source": ["```bash\n", "[pypi]\n", "  username = __token__\n", "  password = pypi-xxxxxxxxxxxxxxxxxxxYjNjNS0xMDExNWMwMzhlNDMiXQAABiDpxiNjoqIT3SJDNrQPP-BJl_AhO7pHErgKvOnS4jzNrQ\n", "```"]}, {"cell_type": "markdown", "id": "dfbe6772", "metadata": {}, "source": ["\n", "8.  安装工具及上传制品\n", "\n"]}, {"cell_type": "markdown", "id": "b772c989", "metadata": {}, "source": ["```bash\n", ">>> pip install twine\n", ">>> twine upload dist/*\n", "```"]}, {"cell_type": "markdown", "id": "dcab4c7d", "metadata": {}, "source": ["\n", "\n", "---\n", "\n", "#### 7.2.5 通过持续部署来发布应用\n", "\n", ">通过集成github action实现自动化发布，是github项目经常使用的一种方式，不用人为手动构建，可以在设定情况下构建包并上传到官方pypi仓库 （比如 打tag）\n", "\n", "1. 进入你的 GitHub 仓库 。\n", "2. 点击 \"Settings\" → \"Secrets and variables\" → \"Actions\"。\n", "3. 在 \"Secrets\" 部分点击 \"New repository secret\"。\n", "4. 填写：\n", "   - Name（名称）：如 `INDEX_PYPI_TOKEN`\n", "   - Value（值）：粘贴你的 API Token\n", "5. 点击 \"Add secret\"。\n", "6. 仓库中添加`.github/workflows/publish-to-pypi.yaml`文件\n", "\n"]}, {"cell_type": "markdown", "id": "e7336f76", "metadata": {}, "source": ["```yaml\n", "\n", "name: Publish to PyPI\n", "\n", "on:\n", "  push:\n", "    tags:\n", "      - \"v*\"  # 仅在创建 tag（如 v1.0.0）时触发\n", "\n", "jobs:\n", "  deploy:\n", "    runs-on: ubuntu-latest\n", "    steps:\n", "      - name: Checkout repository\n", "        uses: actions/checkout@v3\n", "\n", "      - name: <PERSON>up Python\n", "        uses: actions/setup-python@v4\n", "        with:\n", "          python-version: \"3.x\"\n", "\n", "      - name: Install dependencies\n", "        run: |\n", "          python -m pip install --upgrade pip\n", "          pip install build twine\n", "\n", "      - name: Build package\n", "        run: python -m build\n", "\n", "      - name: Upload to PyPI\n", "        env:\n", "          INDEX_PYPI_TOKEN: ${{ secrets.INDEX_PYPI_TOKEN }}\n", "        run: |\n", "          twine upload --username __token__ --password $INDEX_PYPI_TOKEN dist/*\n", "```"]}, {"cell_type": "markdown", "id": "eea08a80", "metadata": {}, "source": ["\n", "\n", "* 每次打tag时，会自动触发编译whl包，并上传至官方仓库\n", "\n", "#### 7.2.6 使用\n", "\n", "1.  然后其他人就可以通过pip来安装你的项目，默认最新版本。\n", "\n"]}, {"cell_type": "markdown", "id": "2943536e", "metadata": {}, "source": ["```bash\n", ">>> pip install my-project\n", "```"]}, {"cell_type": "markdown", "id": "b4413103", "metadata": {}, "source": ["\n", "2.  也可以限制版本安装\n", "\n"]}, {"cell_type": "markdown", "id": "eed90be3", "metadata": {}, "source": ["```bash\n", ">>> pip install \"my-project==0.1.0\"\n", ">>> pip install \"my-project>=0.1.0\"\n", "```"]}, {"cell_type": "markdown", "id": "7eb3572b", "metadata": {}, "source": ["\n", "\n", "---\n", "\n", "### 7.3 镜像制品\n", "\n", "#### 7.3.1 为什么要做Docker镜像\n", "\n", ">**Docker** 是一种开源的容器化平台，用于自动化应用部署。它通过容器技术将应用程序及其依赖打包成一个 ​**轻量**​、**可移植**的单元，能在不同环境中一致运行。\n", "\n", "使用Docker制作镜像制品有下面好处：\n", "\n", "* **​环境一致性：​**避免“在我机器上能跑，但在服务器上不行”的问题。保证在 开发、测试、生产 环境，代码和依赖都一样。\n", "* **​便于部署和扩展：​**一次构建，随处运行，可以在 服务器、Kubernetes、云环境 运行。\n", "* **​行业影响力：​**Docker在业内使用广泛，有自己的官网，基于此有利于更好的传播与使用。\n", "\n", "#### 7.3.2 注册 <PERSON><PERSON>b 账号\n", "\n", "* 访问 [Docker Hub 官网](https://hub.docker.com/) 并注册一个账号。注册的账号名就是 **命名空间（Namespace）**\n", "* 注册成功后，登录你的 Docker Hub 账户。\n", "\n", "---\n", "\n", "#### 7.3.3 创建仓库（​Repository​）\n", "\n", "1.  点击右上角的**​ ​**[Create a repository](https://hub.docker.com/repository/create) (创建仓库) 。\n", "2.  填写仓库信息：\n", "\n", "* Repository Name（仓库名称）：如 `my-project`\n", "* Visibility（可见性）：\n", "  * Public（公开）：任何人都可以拉取你的镜像\n", "  * Private（私有）：只有你或授权用户可以访问\n", "\n", "3.  点击 Create（创建）。\n", "\n", "---\n", "\n", "#### 7.3.4 登录 <PERSON><PERSON>b\n", "\n", "* 在终端或命令行运行：\n", "\n"]}, {"cell_type": "markdown", "id": "71254fff", "metadata": {}, "source": ["```bash\n", ">>> docker login\n", "```"]}, {"attachments": {}, "cell_type": "markdown", "id": "e967901b", "metadata": {}, "source": ["\n", "\n", "* 然后浏览器打开 https://login.docker.com/activate ，并输入终端对应的code `SPQK-WMDJ` 。如果登录成功，会显示：`Login Succeeded`\n", "\n", "![image.png](4_images/img7.png)\n", "\n", "---\n", "\n", "#### 7.3.5 构建并推送镜像\n", "\n", "1.  编辑`Dockerfile`\n", "\n"]}, {"cell_type": "markdown", "id": "5e14732a", "metadata": {}, "source": ["```bash\n", "# 1. 选择基础镜像\n", "FROM python:3.10\n", "\n", "# 2. 设置环境变量\n", "ENV PYTHONUNBUFFERED=1\n", "\n", "# 3. 创建应用文件夹\n", "RUN mkdir /app\n", "\n", "# 4. 拷贝源码\n", "COPY my_project /app/my_project\n", "COPY requirements.txt /tmp/requirements.txt\n", "ENV PYTHONPATH=\"/app:${PYTHONPATH}\"\n", "\n", "# 5. 安装依赖\n", "RUN pip install -r /tmp/requirements.txt \\\n", "    && rm -rf /tmp/requirements.txt\n", "\n", "# 6. 创建非 root 用户，提升安全性\n", "RUN useradd -m myuser\n", "USER <PERSON>\n", "\n", "# 7. 设置默认启动命令 \n", "CMD [\"/bin/bash\"]\n", "```"]}, {"cell_type": "markdown", "id": "51d7d809", "metadata": {}, "source": ["\n", "\n", "2.  构建镜像\n", "\n"]}, {"cell_type": "markdown", "id": "02f204d4", "metadata": {}, "source": ["```bash\n", ">>> docker build  -t username/my-project:0.1.0 .\n", "```"]}, {"cell_type": "markdown", "id": "7d043917", "metadata": {}, "source": ["\n", "\n", "3.  发布镜像\n", "\n"]}, {"cell_type": "markdown", "id": "e3f7adc3", "metadata": {}, "source": ["```bash\n", ">>> docker push username/my-project:0.1.0\n", "```"]}, {"cell_type": "markdown", "id": "1c8ddc81", "metadata": {}, "source": ["\n", "\n", "#### 7.3.6 镜像使用\n", "\n", "1.  拉取镜像\n", "\n"]}, {"cell_type": "markdown", "id": "d5e279e2", "metadata": {}, "source": ["```bash\n", ">>> docker pull username/my-project:0.1.0\n", "```"]}, {"cell_type": "markdown", "id": "50ce9ba8", "metadata": {}, "source": ["\n", "\n", "2.  起容器使用\n", "\n"]}, {"cell_type": "markdown", "id": "e8e60778", "metadata": {}, "source": ["```bash\n", ">>> docker run -it --name my-project username/my-project:0.1.0\n", "```"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}