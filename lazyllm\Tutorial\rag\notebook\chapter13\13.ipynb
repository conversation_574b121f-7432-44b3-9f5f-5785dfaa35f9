{"cells": [{"cell_type": "markdown", "id": "84297deb", "metadata": {}, "source": ["# 13进阶3：RAG+多模态：图片、表格通吃的问答系统\n", "\n", ">在前面的课程中，我们探讨了 RAG（Retrieval-Augmented Generation）的基本原理及其在纯文本处理中的应用。RAG 通过从外部知识库检索相关信息，结合上下文生成更准确、信息丰富的回答，从而提升基于文本的问答系统能力。然而，现实世界中的信息并不局限于文本，例如 PDF 文档中的图片、表格等多模态数据也承载着大量有价值的知识。在某些情况下，这些图文并茂的内容比纯文本更直观、有效。但 RAG 主要依赖文本检索和生成，对 PDF 文件中的图片处理能力较弱，无法直接解析和利用图像信息进行检索或生成，可能导致关键信息遗漏，影响最终回答质量。因此，在 RAG 处理包含重要图片信息的 PDF 文档时，需要结合 OCR（光学字符识别）或计算机视觉技术进行补充，以提高内容解析能力。本节课程将介绍如何在 RAG 系统中处理 PDF 中的图片与表格，并提取其中的关键信息进行问答，从而增强系统对多模态数据的理解与应用。\n", "\n", "## 多模态模型\n", "\n", "### 为什么要引入多模态 ?\n", "\n", "在实际应用中，我们常常需要从合同、报告、产品说明书等多种形式的文档中提取有用信息。这些文档不仅包含丰富的文字内容，还可能包括图片、图表、表格等非文本信息。下面是某页PDF原始视图的信息展示。"]}, {"attachments": {}, "cell_type": "markdown", "id": "fe611707", "metadata": {}, "source": ["\n", "![image.png](13_images/img2.png)\n", "\n", "现在，我们使用普通模型与多模态模型分别读取PDF的内容。在读取包含图文信息的 PDF 时，生成的“可用于检索/生成的文本格式”之间的区别如下：\n", "\n", "![image-2.png](13_images/img1.png)\n", "\n", "我们可以看到原始 RAG 主要依赖对文本的检索与生成，无法充分理解和利用图像、图表中承载的重要内容，会导致相关内容的缺失，这在处理 PDF 等复杂文档时尤为明显。因此，为了让普通 RAG 在更多真实场景下保持高效、智能的表现，我们亟需引入多模态能力，结合 OCR、图像理解等技术，让模型具备“读图识意” 的能力，从而真正实现对多模态信息的智能问答与生成。"]}, {"attachments": {}, "cell_type": "markdown", "id": "ddb191d7", "metadata": {}, "source": ["\n", "### 简介\n", "\n", "在现实世界中，信息从不以单一模态呈现。人类的日常感知依赖于多种模态的协同输入，包括视觉、听觉、语言、触觉等，这些感知共同构成了我们对世界的整体理解。当研究任务或数据涉及多种模态信息时，我们将其称为“多模态问题”（Multimodal Problem）。有效应对这类问题，是推动人工智能系统向类人认知迈进、实现“类人智能”的关键一步。\n", "\n", "![image.png](13_images/img3.png)\n", "\n", "正因如此，多模态大型语言模型（Multimodal Large Language Models，MLLM）应运而生。它是在大型语言模型（LLM）与大型视觉模型（LVM）不断演进的基础上发展起来的。LLM 在语言理解、推理能力、指令跟随、上下文学习和思维链等方面不断突破，推动了自然语言处理技术的广泛应用。然而，它在处理图像、音频等非语言模态信息时仍面临局限。与此同时，LVM 在图像识别、目标检测、图像分割等视觉任务中取得了显著成果，一些模型已能接受语言指令并执行特定视觉任务，但其推理与跨模态能力仍有待提升。这促使研究者尝试融合 LLM 的语言能力与 LVM 的感知能力，从而实现图文等多种模态的信息理解与生成。MLLM 正是这种融合的产物。通过联合建模、跨模态对齐等技术路径，MLLM 可以同时处理文本与图像等多模态输入，在图文问答、多模态检索、辅助创作等任务中展现出更强的理解力与生成力，为人工智能迈向更广阔的认知世界奠定基础。\n", "\n", "### 基本原理\n", "\n", "​      在了解了多模态大模型的概念和发展背景之后，我们接下来深入探讨其基本原理。多模态大模型之所以强大，核心在于它能够理解、对齐并融合来自不同模态的数据，如文本、图像、语音甚至视频，实现统一的信息建模与推理。我们可以从以下三个层次来理解其基本原理：\n", "\n", "**1. 感知层：模态感知与特征提取**\n", "\n", "不同模态的数据具有不同的结构特性，例如：\n", "\n", "- 图像是二维像素网格；\n", "- 语音是连续的声波信号；\n", "- 文本是离散的符号序列；\n", "- 视频是帧序列的组合。\n", "\n", "在感知层，每种模态需要一个专门的编码器来提取其特征。比如：\n", "\n", "- 图像通常使用卷积神经网络（CNN） 或 视觉 Transformer（ViT） 提取空间特征；\n", "- 音频可以使用卷积网络或频谱 Transformer 处理；\n", "- 文本则由 预训练语言模型（如 BERT、GPT） 进行编码。\n", "\n", "这一步的关键是将不同模态的数据转换成统一结构的向量表示（embedding），以便后续处理。\n", "\n", "**2. 对齐层：表示对齐与语义映射**\n", "\n", "​      由于不同模态的 embedding 本质上来自不同空间，它们的语义分布不一致，不能直接融合。这时需要一个“桥梁”模块——连接器（Connector），来实现表示空间的映射和对齐。常见的对齐策略有：\n", "\n", "- MLP 映射：通过一个简单的多层感知机网络将非文本模态的向量映射到文本 embedding 的空间，从而实现空间变换；\n", "- 跨模态注意力（Cross-Attention）：使用图像或音频向量作为 Key/Value，通过 Transformer 解码器模块与文本空间交互；\n", "- 内部融合机制：在模型内部结构层面实现模态间的融合，比如直接将图像 patch 和文本 token 混合输入。\n", "\n", "这一层的核心目标是：建立模态之间的语义关联性，让模型能够“理解”图像中的物体与文本描述之间的关系，或者让语音与文字对齐。\n", "\n", "**3. 理解与生成层：统一语义建模与跨模态推理**\n", "\n", "​      当所有模态的表示被映射到一个统一的空间后，就可以输入到大语言模型（LLM）中，进行深入理解和生成。此时：\n", "\n", "- LLM 扮演“推理中枢”的角色，利用其强大的上下文理解能力，对多模态信息进行整合；\n", "- 通过语言建模目标（如下一词预测、问答等），驱动整个多模态系统学习跨模态推理能力；\n", "- 输出不仅限于文本，也可以是图文对话、图像描述、音频问答等形式。\n", "\n", "我们可以看出多模态大模型并非只是简单地把图像和文本“拼接”在一起，而是通过编码 → 对齐 → 理解的流程，实现了模态间的语义融合与统一推理。这种设计使得 MLLM 具备类人认知能力：能看图说话、听音理解、跨模态问答，甚至进行多模态创作。"]}, {"attachments": {}, "cell_type": "markdown", "id": "b20e7558", "metadata": {}, "source": ["\n", "### 模型架构\n", "\n", "​      从类比人类感知系统的角度来看，多模态大模型的架构可以理解为：编码器就像人的眼睛和耳朵，负责接收视觉、听觉等外部信息，并将其转化为“感知信号”；LLM（大语言模型）则类似于人脑，对这些信号进行理解、推理与生成。\n", "\n", "![image.png](13_images/img4.png)\n", "\n", "多模态大模型的整体结构通常包括三个核心模块：**模态编码器**（Modality Encoder）、**连接器**（Connector） 和**语言模型**（LLM）。\n", "\n", "- 模态编码器（Modality Encoder）\n", "  - 作用：将不同模态的数据（如图像、音频、视频、文本）转化为向量表示（embedding）。\n", "  - 特点：每种模态使用专门的模型进行编码，如 CNN/ViT 处理图像、语音模型处理音频、语言模型处理文本。\n", "- 连接器（Connector）\n", "  - 作用：将非文本模态的向量映射到与文本 embedding 相容或可对齐的空间，解决语义空间不一致的问题。\n", "  - 常见连接方式：MLP 映射：通过多层感知机进行空间转换；Cross-Attention 映射：使用跨模态注意力机制建立模态间交互；模型内部融合：如将图像 patch 与文本 token 混合输入，实现底层结构级融合。\n", "- 语言模型（LLM）\n", "  - 作用：接收统一后的向量表示，执行理解与生成任务；\n", "  - 特点：作为推理核心，LLM 支持图文对话、问答、描述等任务，是整个多模态系统的关键部分。\n", "\n", "多模态输入（如图像、音频、视频等）和文本数据在形式上不同，因此需要先通过各自的编码器将其转化为向量表示（embedding）。但这些向量初始时并不一定处于与文本 embedding 相同的空间中。这时就需要连接器模块，它的作用是将多模态的 embedding 映射到与文本 embedding 相容或可对齐的空间。常见的三种连接方式包括前面提到的MLP 映射、Cross-Attention 映射以及模型内部融合。无论我们使用哪种连接方式，最终的核心还是预训练好的 LLM，它对统一后的表示进行理解和生成，是整个多模态处理流程中最关键的推理单元。"]}, {"attachments": {}, "cell_type": "markdown", "id": "80d13e7d", "metadata": {}, "source": ["\n", "### 主流开源模型\n", "\n", "![image.png](13_images/img5.png)\n", "\n", "在多模态大语言模型（MLLM）兴起之前，已有不少工作致力于多模态学习，主要分为两类技术路线：\n", "\n", "- 判别式范式（Discriminative Paradigm）：以 CLIP 为代表，侧重于学习图文匹配与对齐，主要用于图像分类、检索等任务；\n", "- 生成式范式（Generative Paradigm）：以 OFA、VL-T5、Flamingo 等为代表，强调跨模态生成，如图像描述、视觉问答等。\n", "\n", "MLLM 可看作是生成式多模态模型的发展延续，在更强大的语言模型支撑和全新的训练范式加持下，能力边界被大幅拓展。目前主流的开源 MLLM 通常基于大规模预训练语言模型（如 LLaMA、Vicuna），结合图像编码器（如 CLIP-ViT、BLIP）和连接器模块，将图像信息有效映射到语言模型可处理的表示空间。多数模型采用多模态指令微调策略，使其具备“看图说话”“图文对话”“跨模态推理”等复杂能力。典型的开源项目包括 LLaVA、MiniGPT-4、InstructBLIP 和 Fuyu，覆盖了轻量化部署、对话体验优化和推理能力提升等多个方向。这些模型普遍采用“图像编码器 + 连接器 + LLM”的模块化架构，结合不同的训练目标与任务场景，持续推动多模态模型在泛化能力和实际应用层面的突破。\n", "\n", "### 在LazyLLM中使用多模态大模型\n", "\n", "**LazyLLM 已支持多模态大模型接入**（如图文问答、图文理解等场景），可通过以下方式快速启动并使用。以智谱的多模态模型 **`glm-4v-flash`** 为例，使用 LazyLLM 启动服务："]}, {"cell_type": "code", "execution_count": null, "id": "212d8ec8", "metadata": {}, "outputs": [], "source": ["import lazyllm\n", "\n", "chat = lazyllm.OnlineChatModule(source=\"glm\", model=\"glm-4v-flash\")\n", "lazyllm.WebModule(chat, port=23333, files_target=chat).start().wait()"]}, {"attachments": {}, "cell_type": "markdown", "id": "8efd7afb", "metadata": {}, "source": ["> 除了 `glm-4v-flash`，LazyLLM 同样支持多种主流多模态大模型，如Qwen-VL 系列、OpenAI GPT-4V等，但需要用户自行申请和配置API Key 。\n", "\n", "启动后，用户可通过 **WebUI** 页面上传图片、输入问题，实现图文混合问答体验。\n", "\n", "![image.png](13_images/img6.png)"]}, {"attachments": {}, "cell_type": "markdown", "id": "57f012a8", "metadata": {}, "source": ["\n", "## 多模态 RAG\n", "\n", "### RAG多模态架构\n", "\n", "​      多模态 RAG（Retrieval-Augmented Generation）扩展了传统 RAG 的能力，使其能处理文本、图像、音频等多种模态，实现更丰富的信息检索与生成。其核心在于跨模态对齐与融合，依赖多模态嵌入（Multimodal Embedding）将不同类型数据映射到统一表示空间，以便高效检索与生成。CLIP、BLIP 等模型的发展推动了这一技术，使多模态 RAG 更精准地整合多模态信息，增强理解与生成能力。下面我们将从整体架构出发，带领读者深入了解多模态 RAG 的基本原理及其作用。\n", "\n", "#### 多模态 RAG VS 基础 RAG\n", "\n", "​      在前面的 RAG 架构图中，我们主要针对文本数据进行了检索与生成。然而，要支持多模态输入和输出，需要对原有 RAG 架构进行一定的调整和扩展。  \n", "\n", "![image.png](13_images/img7.png)\n", "首先，需要修改的模块包括：  \n", "\n", "- 检索模块：原本仅支持文本检索，现在需要扩展为支持多模态检索，例如图像、音频等信息的索引和匹配。  \n", "- 生成模块：原始 RAG 仅针对文本生成，现在需要扩展支持多模态输出，如文本结合图像、音频的生成能力。  \n", "\n", "其次，需要新增的模块包括以下四个部分：  \n", "\n", "| **模块**         | **功能**                                                     |\n", "| ---------------- | ------------------------------------------------------------ |\n", "| **多模态编码器** | 用于对不同模态的数据（文本、图像、音频等）进行编码，以便统一表示并用于检索和生成。 |\n", "| **多模态融合器** | 用于融合不同模态的信息，使其能够协同作用，提高生成内容的准确性和丰富度。 |\n", "| **跨模态检索器** | 支持输入多种数据格式，并能在多模态知识库中找到相关信息。     |\n", "| **多模态解码器** | 负责将生成结果解码为多种形式，如文本、图片、语音等，以适应不同的输出需求。 |"]}, {"attachments": {}, "cell_type": "markdown", "id": "9c171314", "metadata": {}, "source": ["\n", "### 多模态RAG的核心组件\n", "\n", "为了支持多模态输入和输出，调整和扩展后的多模态RAG 流程图如下。\n", "\n", "![image.png](13_images/img9.png)\n", "\n", "下面简单讲一下每个组件的功能以及相关输入输出示例。\n", "\n", "#### **多模态编码与融合**\n", "\n", "在多模态RAG系统中，多模态编码模块是将不同类型的输入（如文本、图像、音频等）统一映射到向量空间的关键组件。其主要作用是对用户查询和知识库内容进行编码，以便进行相似度检索与上下文构建。相比于传统仅处理文本的 RAG，多模态 RAG 需要将图文等多源信息编码进行统一编码，从而实现跨模态信息的理解与匹配。\n", "\n", "![image-2.png](13_images/img8.png)\n", "\n", "1. **文本编码常用模型**：\n", "   1. **BAAI/bge系列**（如 bge-m3：支持多语言和长文本处理，生成高质量语义嵌入）\n", "   2. **Cohere Embed**（提供高效的语义编码和上下文理解，适用于文本检索与生成任务）\n", "   3. **GTE-large**（专注于高精度语义匹配与检索，适合大规模文本数据处理）\n", "2. **多模态编码常用模型**：\n", "   1. **图像编码**：NFNet-F6、ViT、CLIP ViT\n", "   2. **音频编码**：Whisper、CLAP\n", "   3. **视频编码**：CMVC"]}, {"attachments": {}, "cell_type": "markdown", "id": "e3a724bb", "metadata": {}, "source": ["\n", "在RAG系统中，多模态数据编码的融合是一个核心问题。为实现跨模态信息的有效交互与检索，我们需要将不同模态的数据映射到一个统一的向量空间中。这样，不论是文字还是非文字信息，都可以在同一语义空间中进行相似性度量、检索和生成任务。这里介绍三种常用方法。\n", "\n", "**1.** **统一模态**\n", "\n", "将所有模态转化成相同模态（通常是文本）来达到统一向量空间的目的。常见做法为，对于非文本模态（如图像、音频、表格等），首先通过专门的模型将其转换为具有语义信息的描述性文本，然后统一采用文本编码器进行向量化处理。例如，图像可以通过图像描述生成模型转化为自然语言描述，音频可通过语音识别模型转写为文本，结构化数据（如表格）则可借助表格理解或摘要模型生成文本说明。最终，这些文本内容被统一输入到文本编码模型中，得到位于同一语义空间的向量表示。\n", "\n", "**2.** **跨模态编码**\n", "\n", "通过联合嵌入技术，可以直接将多模态数据编码到同一个统一的向量空间中，无需将非文本模态转换为中间形态。目前，已有多种模型实现了多模态混合编码，能够同时处理文本、图像、音频等多种模态输入。**常见的多模态编码器有图像类的CLIP等，音频类的CLAP等**。这种编码方式更有效地保留了原始模态的信息，可提升下游任务表现，同时简化了数据流程，为RAG等系统提供一致的向量接口，更具工程优势。\n", "\n", "**3. 分离检索**\n", "\n", "第三种方式使用多个专门模型分别处理不同模态的数据（如图像、音频、文本），再在检索阶段融合结果。这种方法具备较高的灵活性，能针对每种数据类型采用最优模型，从而提升检索的准确性与相关性。尽管增加了系统复杂性和实现难度，但在强模态模型不断发展的背景下，这种多模型协作的多模态 RAG 架构正变得越来越有吸引力。\n", "\n", "#### **多模态检索模块**\n", "\n", "**1.基础检索**\n", "\n", "处理单一模态的查询，如文本到文本、图像到图像的匹配。\n", "\n", "| **输入**                                                     | **输出**                                                     |\n", "| ------------------------------------------------------------ | ------------------------------------------------------------ |\n", "| 文本查询：\"一只黑白相间的小猫\"                               | 相关文本段落，如\"黑白相间的小猫通常是美国短毛猫\"             |\n", "| 图像查询：![image.png](13_images/img11.png) | 相关图像，如下：![image-2.png](13_images/img10.png) |\n"]}, {"attachments": {}, "cell_type": "markdown", "id": "44bf6928", "metadata": {}, "source": ["\n", "当输入为文本查询，如`\"一只黑白相间的小猫\"`时，则会输出相关文本段落，如 `\"黑白相间的小猫通常是美国短毛猫\"`；而当输入为图像查询时，如一张黑白相间小猫的照片时，则会输出相关图像，如一张黑白猫的图片。检索通常依赖嵌入相似度计算，如余弦相似度：\n", "\n", "$$\\begin{equation} \\text{sim}(q, d) = \\frac{\\mathbf{q} \\cdot\\mathbf{d}}{\\|\\mathbf{q}\\|\\|\\mathbf{d}\\|} \\end{equation}$$\n", "\n", "其中q是查询文档的嵌入向量，d是候选文档的嵌入向量。如果计算出的相似度高于某个阈值（例如0.9），则认为该文档相关。\n", "\n", "**2.跨模态检索**\n", "\n", "支持跨模态查询，例如文本查询匹配图像，或图像查询匹配文本。\n", "\n", "| **输入**                                                     | **输出**                                                     |\n", "| ------------------------------------------------------------ | ------------------------------------------------------------ |\n", "| 文本查询：\"一只黑白相间的小猫\"                               | 相关图像，如一张黑白猫的图片，如下：![image.png](13_images/img12.png) | 相关描述信息，如\"这是一只黑白相间的美国短毛猫，坐在棕色的地板上。\" |\n", "\n", "当输入为文本时，如`\"小猫长什么样？\"`，则会输出最相关的猫的图片；而当输入为猫的照片时，则会输出相关描述文本，如 `\"这是一只黑白相间的美国短毛猫\"`"]}, {"attachments": {}, "cell_type": "markdown", "id": "d3801cfa", "metadata": {}, "source": ["\n", "#### **多模态生成模块**\n", "\n", "原有 RAG 仅支持文本生成，现在扩展为支持多模态输出，例如生成带有图片的回答或结合语音的解释。\n", "\n", "| **输入**                           | **输出**                                                     |\n", "| ---------------------------------- | ------------------------------------------------------------ |\n", "| 文本查询：\"黑白相间的猫长什么样？\" | 图文回答：文本 + 一张黑白猫的图片，如下：![image.png](13_images/img13.png) |\n", "\n", "当输入为文本查询时，如`\"黑白相间的猫长什么样？\"` ，则会将相关检索结果（文本、图片）传给大模型，最后输出图文回答，如相关文本 + 一张黑白猫的图片。\n", "\n", "文本生成依赖自回归 Transformer 模型计算概率：\n", "\n", "$$\\begin{equation} P(y | x) = \\prod_{t=1}^{T} P(y_t | y_{<t}, x) \\end{equation}$$\n", "\n", "其中，x是输入的检索内容，yt是当前生成的词，y<t是前面已经生成的词。假设模型可能按步骤生成了P(\"A\")=0.8；P(\"black\")=0.9；P(\"and\")=0.85；P(\"white\")=0.95，最终的输出则为 \"A black and white cat\"。"]}, {"cell_type": "markdown", "id": "0e90e41a", "metadata": {}, "source": ["\n", "#### 三阶段示例\n", "\n", "接下来以「旅行问答」场景为例，介绍多模态RAG三个阶段的结果输出。\n", "\n", "**1. 编码阶段**\n", "\n", "输入：\n", "\n", "- 用户查询（文本+图）：\"这张照片里的建筑是什么风格？\" + [拜占庭风格教堂照片]\n", "- 知识库内容：\n", "  - 文本1：《拜占庭建筑特点》文档（含穹顶、马赛克等关键词）\n", "  - 图片2：哥特式教堂照片\n", "  - 图片3：威尼斯圣马可大教堂照片\n", "  - ...\n"]}, {"attachments": {}, "cell_type": "markdown", "id": "09efdb2b", "metadata": {}, "source": ["\n", "【query图片】\n", "\n", "![image.png](13_images/img16.png)\n", "\n", "【图片2】\n", "\n", "![image-2.png](13_images/img14.png)\n", "\n", "【图片3】\n", "\n", "![image-3.png](13_images/img15.png)\n", "\n", "编码结果示意："]}, {"cell_type": "code", "execution_count": null, "id": "f8389648", "metadata": {}, "outputs": [], "source": ["# 多模态编码 （如CLIP混合编码器实现）\n", "query_text_embed = [0.23, -0.57, ..., 0.89]  # query的embedding，均为512维\n", "doc1_text_embed = [0.20, -0.52, ..., 0.91]   # 文本1的embedding， 与 query_text_embed 相似\n", "\n", "query_img_embed = [0.67, 0.12, ..., -0.33]  # query中图片的embedding\n", "img2_embed = [0.02, 0.45, ..., 0.11]        # 图片2的embedding， 与 query_img_embed 差异较大\n", "img3_embed = [0.63, 0.09, ..., -0.30]       # 图片3的embedding， 与 query_img_embed 相似\n", "\n", "# 多模态融合表示（以加权平均为例）\n", "query_fused = [0.45, -0.22, ..., 0.28]      # 文本0.4 + 图像0.6权重"]}, {"cell_type": "markdown", "id": "4ff11959", "metadata": {}, "source": ["**2. 检索阶段**\n", "\n", "向量数据库操作：\n", "\n", "1. 分别计算 `query_fused` 与所有知识库向量的余弦相似度\n", "2. 返回Top3结果：\n", "\n", "| 排名 | 内容类型 | 相似度 | 片段示意                          |\n", "| ---- | -------- | ------ | --------------------------------- |\n", "| 1    | 图片3    | 0.93   | 圣马可大教堂（拜占庭风格照片）    |\n", "| 2    | 文本1    | 0.82   | \"拜占庭建筑以穹顶和金色马赛克...\" |\n", "| 3    | 图片2    | 0.31   | 哥特式教堂尖顶照片                |\n", "\n", "检索结果JSON表示："]}, {"cell_type": "code", "execution_count": null, "id": "8cc63a78", "metadata": {"attributes": {"classes": ["JSON"], "id": ""}}, "outputs": [], "source": ["{\n", "  \"retrieved_results\": [\n", "    {\n", "      \"image_path\": \"圣马可大教堂.jpg\",\n", "      \"score\": 0.93,\n", "      \"modality\": \"multimodal\"\n", "    },\n", "    {\n", "      \"content\": \"拜占庭建筑特点包括...\",\n", "      \"score\": 0.82,\n", "      \"modality\": \"text\" \n", "    }\n", "  ]\n", "}"]}, {"cell_type": "markdown", "id": "3b8d0bb3", "metadata": {}, "source": ["**3. 生成阶段**\n", "\n", "多模态LLM输入："]}, {"cell_type": "code", "execution_count": null, "id": "32de9712", "metadata": {"attributes": {"classes": ["<PERSON><PERSON>"], "id": ""}, "vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["[USER_QUERY] \n", "  文本: \"这张照片里的建筑是什么风格？\"  \n", "  图像: <拜占庭教堂照片>\n", "\n", "[CONTEXT]\n", "  1. [图片] <圣马可大教堂照片>\n", "  2. [文本] 拜占庭建筑特点: \"圆形穹顶、金色马赛克装饰...\"\n", "\n", "[OUTPUT]"]}, {"attachments": {}, "cell_type": "markdown", "id": "b85b8963", "metadata": {}, "source": ["生成结果：\n", "\n", "> \"您照片中的建筑具有典型的拜占庭风格特征：\n", ">\n", "> 1. 穹顶结构（如圣马可大教堂的中央穹顶）\n", "> 2. 马赛克装饰（可见照片中金色反光部分）\n", ">\n", "> ...\n", "\n", "## 基于 PDF 文档的多模态 RAG\n", "\n", "以PDF文档为例，演示多模态RAG的整体实现流程。为了对PDF文档进行完整的嵌入，首先通过布局识别模型对文档段落、标题以及图标等其他元素进行识别和提取。下图展示了基于OCR文档解析的图文RAG系统流程，接下来我们将对各个步骤进行简要介绍，包括**文档解析、多模态嵌入**以及**查询与生成**三个关键步骤。\n", "\n", "![image.png](13_images/img17.png)"]}, {"cell_type": "markdown", "id": "a490780a", "metadata": {}, "source": ["\n", "### **1. 文档解析**\n", "\n", "#### PDF 格式概述与解析方法\n", "\n", "##### **1.PDF的格式简介**\n", "\n", "PDF（Portable Document Format，便携式文档格式）是一种独立于应用程序、硬件和操作系统的文件格式，主要用于共享和查看文档。PDF 文档的主要特点包括：\n", "\n", "- 具有固定的布局和格式，在不同设备和操作系统上保持一致。\n", "- 可包含文本、图像、链接、按钮、表单字段、音频、视频及业务逻辑等内容。\n", "- 适用于文档存储、交换和打印，广泛应用于办公和学术领域。\n", "\n", "##### **2.PDF 的主要类型**\n", "\n", "根据来源不同，PDF 可分为以下两类。\n", "\n", "① 机器生成的 PDF：\n", "\n", "- 由专业软件（如 Adobe Acrobat、Microsoft Word、Typora 等）创建。\n", "- 内部包含可选中、可搜索、可编辑的文本、图像和超链接。\n", "- 适用于直接解析和结构化处理。\n", "\n", "② 扫描生成的 PDF\n", "\n", "- 由扫描设备或拍摄图片转换而成，通常存储为位图格式。\n", "- 其内容不可直接搜索和编辑，仅包含图像。\n", "- 需要 OCR（光学字符识别）技术进行文本提取和解析。\n", "\n", "##### **3.PDF 的解析方法**\n", "\n", "针对不同类型的 PDF，解析方法有所区别：\n", "\n", "① 机器生成的 PDF\n", "\n", "- 可直接使用 Python 库进行解析，如 pdfminer、pdfplumber 等。\n", "- 这些工具能够提取文本、图像、表格等结构化信息。\n", "\n", "② 扫描生成的 PDF\n", "\n", "- 需要 OCR 技术将图像转换为文本，常用方法包括深度学习模型（如 Tesseract OCR、LayoutLM）或云端 OCR 服务。\n", "- 深度学习方法通常在复杂版式和多语言场景下具有更优的解析效果。\n", "\n", "通过选择合适的解析方法，可以高效地从 PDF 文档中提取有价值的信息，为文档理解与处理提供支持。\n", "\n", "#### PDF 文档解析与结构化处理\n", "\n", "PDF 解析的目标是从不可编辑的文档中提取文本并转换为标准编码格式存储，同时保留原始结构信息。由于排版复杂，直接提取文本可能导致结构信息丢失，如标题、段落、表格等，而这些信息对于语义理解和逻辑推理至关重要。因此，PDF 解析通常被视为广义 OCR 任务，包含文本识别（OCR）和布局分析两个步骤。\n", "\n", "- **OCR 提取文本**：OCR（光学字符识别）技术用于将纸质文档或扫描图片中的文字转换为可编辑、可搜索的电子文本。例如，PaddleOCR 和 EasyOCR 等开源工具能够高效识别 PDF 中不可编辑的文本，并将其存储为纯文本（TXT）格式。\n", "- **布局分析**：布局分析的目标是恢复 PDF 的原始文本组织结构，识别各文本块之间的关系，并按照文档原有格式组织文本。例如，LayoutLM 模型能够联合建模文档中的文本、布局和图像信息，准确识别文本在页面中的位置及其排版结构。此外，对于包含表格、公式等复杂内容的 PDF，可结合专门的表格分析模型进行解析，以保留文档的完整信息。\n", "\n", "在实际应用中，magic-pdf 是一个强大的 PDF 解析工具，它封装了文本提取、布局恢复、表格和公式分析等功能，并提供了便捷的 Python 接口。通过使用 magic-pdf，可以显著简化 PDF 解析流程，提高多模态信息处理的效率和准确性，为后续的文档理解、检索和生成任务提供更丰富的支持。"]}, {"attachments": {}, "cell_type": "markdown", "id": "6e003f5b", "metadata": {}, "source": ["### **2. 多模态嵌入**\n", "\n", "在完成文档解析提取文档中的文档和图表之后，我们需要对文档进行嵌入。在拿到解析完成的文档时（即提取的文本和图像集合），最朴素的想法是对文本和图像分别进行向量嵌入得到向量表示。这种方法虽然简单，但它有一个显著的缺点：无法捕捉不同模态（如文本与图像）之间的内在关联。具体来说，文本和图像分别被映射到两个独立的向量空间，这样它们之间就缺乏直接的语义联系。如果想要实现RAG就需要对文本和图像分别进行检索，并在检索后进行某种形式的重排序才能找出最相关的内容。这种方法虽然实现起来比较简单，但从系统性能以及表现来看，在查询阶段，当系统需要从多个模态中提取最相关的信息并进行重排序时，涉及到的模态数量越多，检索和排序的任务就越复杂。\n", "\n", "为了降低在线阶段的复杂度，我们可以在文档的离线嵌入阶段，将来自不同模态的信息直接映射到一个共享的向量空间中。这样，在检索和生成的在线阶段，系统只需要在一个统一的向量空间内进行操作，从而避免了多个模态之间的单独检索和后续的重排序过程，大大提升了系统的效率和响应速度。通过这种方式，不仅能够有效地降低在线推理的复杂度，而且还能够使文本和图像之间的关系更加紧密，进而提升多模态任务的性能。下图提供了两种统一向量空间的方法，一种是直接将多个模态的数据映射到同一个向量空间（左侧），另一个是先进行模态统一，再进行向量嵌入（右侧）。\n", "\n", "![image.png](13_images/img18.png)\n", "\n", "左侧为利用多模态模型进行映射方法，右侧为统一数据模态后进行向量嵌入方法\n", "\n", "**（1）多模态直接嵌入统一空间**\n", "\n", "当我们只涉及文本与图像的嵌入问题时（我们可以把表格看作是图像，或者在解析阶段将表格解析为格式化文本），可以通过特定的模型（例如 CLIP、VisualBERT 等）映射到同一个向量空间。这种方法能够使得不同模态之间的语义关联更加紧密，并且使得检索时不需要分开处理各个模态。通过共享的向量空间，文本和图像可以在相同的语义空间中进行比较和排序，从而提高多模态检索和生成的效率和准确性。\n", "\n", "**（2）模态统一后再嵌入**\n", "\n", "这种方法通常会首先通过某些转换模型（如文本描述生成模型）将图像等非文本模态转化为文本形式。例如图像可以通过视觉模型生成与之相关的文本描述，表格可以通过表格分析模型转化为结构化的文本信息。在完成模态统一后，这些文本数据可以通过文本嵌入模型（如 BERT、T5、GPT 等）进行进一步的嵌入。这种方式的好处是可以利用现有成熟的文本嵌入技术来处理不同模态的信息，但缺点是图像、视频等模态的原始信息可能在转化为文本过程中丢失了一部分重要细节，且生成的文本可能无法完全保留原模态的丰富语义。\n", "\n", "这两种方法各有优缺点，选择哪种方式取决于具体的应用需求和模型设计的目标。如果目标是最大限度地保留各模态的原始信息并且提升多模态数据间的关联性，第一种方法可能更为适合。反之，如果项目中涉及到大量的非文本模态，且可接受通过文本的转换来进行表征，那么第二种方法也可以是一种有效的解决方案。不管使用哪种方法，都只涉及了RAG的离线流程，您只需要在文本RAG系统中更改文档入库相关的流程，即可得到一个最基础的多模态RAG。通常来说如果对图表生成了详尽准确的描述，或使用了性能较好的嵌入模型，会对文档内容有较好的补充作用，有助于在响应用户问题时通过图表内容给出更加详细的解答。\n", "\n", ">**利用多模态模型解析图片：自动提取信息并生成QA对**\n", ">\n", ">除了上文中提到的两种图片解析方法，在解析图片时，我们还可以使用多模态模型如： InternVL-Chat-V1-5 来自动提取图片中的关键信息，并生成相应的问答（QA）对。这种方法的核心思路是：首先，多模态模型结合视觉与文本理解，分析图片内容，包括物体、文字、场景、结构等；然后，基于提取的信息，生成相关问题及答案，帮助用户快速理解图片的核心内容。例如，在解析一张论文中的示意图或实验结果图时，模型可以识别出图片的标题、数据趋势、关键结论等信息，并自动生成「该实验的主要结论是什么？」等问题及对应答案，从而提升论文内容的理解和信息获取的效率。"]}, {"attachments": {}, "cell_type": "markdown", "id": "df4e7a12", "metadata": {}, "source": ["\n", "### **3. 生成图文并茂的响应**\n", "\n", "如果在处理文档中的图表时，系统仅仅输出与图表相关的描述而无法展示图表本身，这可能会让用户感到信息呈现不够直观和生动。而如果能够在生成的回答中同时展示图表和相关描述，效果无疑会更加吸引人和具有价值。目前输出图文并茂的内容对大语言模型来说仍然是一个具有挑战性的任务，但大多数模型已经能够生成准确的 Markdown 格式文档。Markdown 是一种轻量级的标记语言，它不仅支持文本的格式化，也允许嵌入图片和其他多媒体元素。因此，在系统生成的 Markdown 文档中，我们可以通过图像链接的方式将图表或图片展示出来。您可以在 RAG 系统的文档处理流程中，添加以下三个操作得到图文并茂的输出：\n", "\n", "- **格式化保存图表url**：文档解析时，系统需要识别并提取其中的图表和图像内容。对于每一个图像，您可以通过文档中的图表描述或图像理解模型生成描述性文本，将其转化为图像的描述节点。同时，您需要将这些图像链接（例如存储在某个图像托管服务上的url）与图像描述一并保存为markdown语法`![alt text](image_url）`；\n", "- **节点分类**：在检索阶段，检索组件返回相似节点后，需要根据节点的类型进行分类，如果召回的节点包含图像节点需要为后续生成阶段进行相关提示词处理；\n", "- **大模型提示词修改**：您可能需要为RAG生成答案的大模型提供提示词，例如：如果答案需要展示图像，请给出图像相关链接和解释。\n", "\n", "![image.png](13_images/img19.png)\n", "\n", "通过上述方式，您可以让 RAG 系统在生成答案时通过图像和图表增强答案的表现力和可理解性。这是一种简单的将原图作为答案中一部分的输出方法，实际上您也可以通过调度多个模型实现图表生成的功能，您可以关注下期Agent教学中的相关部分进行相关学习。"]}, {"attachments": {}, "cell_type": "markdown", "id": "e7a6aedc", "metadata": {}, "source": ["\n", "## 多模态内容向量化效果优化技巧\n", "\n", "在多模态 RAG 系统中，向量化是实现跨模态语义对齐和相似度检索的基础。然而，在实际应用中，图像、表格等非文本模态的信息在编码过程中常会出现“语义缺失”或“语义漂移”等问题，导致向量表达与原始内容理解不一致，影响最终检索效果。为提高多模态向量化的准确性与可用性，可以从以下几个方面入手优化：\n", "\n", "1.**文本补全：结合图像标题与注解等文本信息**\n", "\n", "图像或图表在知识库中通常配有**标题（title）**、**图注（caption）或周边说明文本**。这些文本往往高度浓缩了图像的语义信息，是图像内容的最佳语义补充来源。\n", "\n", "**优化策略**：\n", "\n", "- 将生成的图片描述与图片注解等拼接后再做统一编码；\n", "- 将图像本身与其标题、图注等文本**拼接后作为联合输入**，通过联合编码模型（如 CLIP）进行向量生成；\n", "\n", "这种方式能够显著提升图像表示在跨模态检索任务中的表达能力和语义准确性。\n", "\n", "**示例及效果**：\n", "\n", "- **场景**：一张知识库中的科研论文插图\n", "- **图像内容**：一张展示“Transformer 架构”的示意图。\n", "\n", "![image.png](13_images/img20.png)\n", "2.**结构化生成：预先从多模态数据中提取 QA 对**\n", "\n", "图像和表格等多模态信息往往承载复杂关系或关键事实信息，单纯依赖嵌入式编码容易遗漏语义细节。通过**结构化问答生成**的方式提取隐含信息，是提升检索有效性的又一有效手段。\n", "\n", "**优化策略**：\n", "\n", "- 使用 OCR、图像理解模型或表格抽取模型对图片/表格进行解析；\n", "- 结合 LLM 从解析结果中自动生成“**问题-答案对（QA对）**”或“摘要性文本”；\n", "- 将这些结构化 QA 对作为知识增强材料与原始图像联合编码，或将这些 QA 对加入向量数据库。\n", "\n", "这种策略尤其适合用于图表数据、统计报表或医疗影像等对细节和结构化信息敏感的场景。"]}, {"attachments": {}, "cell_type": "markdown", "id": "18cb085e", "metadata": {}, "source": ["\n", "**示例**：\n", "\n", "**场景背景**：企业内部运营系统中一张月度 KPI 报告截图，图表为 PDF 或图片格式，无法直接查询 **图表内容**：柱状图显示2024年1月到3月的销售额与客户增长数，数据如下：         \n", "\n", "| 月份 | 销售额（万元） | 新增客户数（人） |\n", "| ---- | -------------- | ---------------- |\n", "| 1月  | 132            | 58               |\n", "| 2月  | 147            | 76               |\n", "| 3月  | 125            | 52               |\n", "\n", "![image.png](13_images/img21.png)\n", "\n", "**效果**：\n", "\n", "- 优化前：直接编码仅会得到“图表”、“数值”等粗糙标签。\n", "- 优化后：如 KPI 图表，通过自动生成 Q&A（“哪个月销售最高？”）能显著增强对图像数据的查询和理解能力。\n", "\n", "3.**上下文增强：将上下文一同编码**\n", "\n", "很多图像的信息需要**上下文语境**进行理解，单独编码图像容易丧失语境关联性。通过上下文增强策略，可以实现图像与其出现位置的文本、段落的语义联动。\n", "\n", "**优化策略**：\n", "\n", "- 在输入图像编码模型前，将其相关上下文文本一并拼接或融合输入，构建“**图+文上下文窗口**”；\n", "- 使用多模态混合编码模型或统一模态后对其进行联合编码；\n", "\n", "该策略适用于图像与正文高度耦合的任务场景（如新闻、教材、网页百科等），能够帮助模型更好地把握图像语义在上下文中的真实意图。"]}, {"attachments": {}, "cell_type": "markdown", "id": "f30deda0", "metadata": {}, "source": ["\n", "**示例及效果**：\n", "\n", "**场景**：某医院的电子病历系统中，包含胸部X光图像，以及报告正文文本。 **图像内容**：胸部X光图\n", "\n", "![image.png](13_images/img22.png)"]}, {"attachments": {}, "cell_type": "markdown", "id": "ae76808a", "metadata": {}, "source": ["\n", "4. **微调多模态模型：提升模型在特定多模态领域内的适应能力**\n", "\n", "- **示例**：\n", "\n", "医学图像领域，基于多模态模型实现简单的诊断。甚至就不需要额外上下文信息。\n", "\n", "![image.png](13_images/img23.png)\n", "\n", "**图文问答：“大脑的区域是否梗塞？”。**\n", "\n", "❌ 普通通用模型：无法做出诊断。\n", "\n", "✅ 微调后的模型：是的。**大脑的区域存在梗塞。**"]}, {"attachments": {}, "cell_type": "markdown", "id": "7e8b403b", "metadata": {}, "source": ["\n", "- 数据简介\n", "\n", "![image.png](13_images/img25.png)\n", "\n", "VQA-RAD 是一个关于放射影像的问题-答案对数据集。\n", "\n", "- 数据集用途\n", "  - 训练和测试医学影像VQA（视觉问答）系统\n", "  - 支持开放式问题（如“病灶位置？”）和二元问题（如“是否存在肿瘤？”）\n", "- 数据来源\n", "  - 基于MedPix（开放医学影像数据库）\n", "  - 由临床医生手动标注，确保专业性\n", "- 核心优势\n", "  - 首个专注放射影像的VQA数据集\n", "  - 结构清晰，覆盖临床常见问题类型\n", "\n", "|      | **训练集** | **测试集** |\n", "| ---- | ---------- | ---------- |\n", "| 问题 | 1,793      | 451        |\n", "| 图像 | 313        | 203        |\n", "\n", "- 数据处理\n", "  - 数据获取\n", "  - ```<PERSON>\n", "    from datasets import load_dataset\n", "    dataset = load_dataset(\"flaviagiammarino/vqa-rad\")\n", "    ```\n", "\n", "  - 处理前\n", "  - ```JSON\n", "    {\n", "        \"image\": <PIL.JpegImagePlugin.JpegImageFile image mode=RGB size=566x555>,\n", "        \"question\": 'are regions of the brain infarcted?',\n", "        \"answer\": 'yes'\n", "    }\n", "    ```\n", "\n", "  - 处理后（OpenAI格式）：\n", "  - ```JSON\n", "    [\n", "      {\n", "        \"messages\": [\n", "          {\n", "            \"content\": \"<image>are regions of the brain infarcted?\",\n", "            \"role\": \"user\"\n", "          },\n", "          {\n", "            \"content\": \"yes\",\n", "            \"role\": \"assistant\"\n", "          }\n", "        ],\n", "        \"images\": [\n", "          path/to/train_image_0.jpg\"\n", "        ]\n", "      },\n", "    ```\n", "- 微调模型\n", "  - ```<PERSON>\n", "    import lazyllm\n", "    model_path = 'path/to/Qwen2.5-VL-3B-Instruct'\n", "    data_path = 'path/to/vqa_rad_processed/train.json'    # 需要将环境中的transformers和llamafactory升级到最新的开发分支\n", "    m = lazyllm.TrainableModule(model_path)\n", "        .mode('finetune')\n", "        .trainset(data_path)\n", "        .finetune_method(\n", "            (lazyllm.finetune.llamafactory,{\n", "                'learning_rate': 1e-4,\n", "                'cutoff_len': 5120,\n", "                'max_samples': 20000,\n", "                'val_size': 0.01,\n", "                'num_train_epochs': 2.0,\n", "                'per_device_train_batch_size': 16,\n", "            }))\n", "    m.update()\n", "    ```\n", "\n", "  - 模型配置：\n", "    - `model_path`指定了我们要微调的模型，这里我们用Qwen2.5-VL-3B-Instruct，直接指定其所在路径即可；\n", "  - 微调配置：\n", "    - `.mode`设置了启动微调模式`finetune`；\n", "    - `.trainset` 设置了训练用的数据集路径；\n", "    - `.finetune_method` 设置了用哪个微调框架及其参数，这里使用的是 `llamafactory` 框架（一个支持 LoRA、QLoRA 等高效微调技术的库）\n", "    - `learning_rate:1e-4`学习率，表示模型每一步更新参数的幅度。较高值训练更快但可能不稳定。\n", "    - `cutoff_len: 5120`输入序列的最大长度，超过这个长度的文本会被截断。适用于长对话或长描述任务。\n", "    - `max_samples: 20000`用于训练的最大样本数。如果你不想训练整个数据集，可以限制在一定数量。\n", "    - `val_size: 0.01`验证集占比。这里是 1%，意味着 99% 的数据用于训练，1% 用于评估模型训练过程中的性能。\n", "    - `num_train_epochs: 2.0`训练轮数。每个 epoch 表示模型看完整个训练集一次。\n", "    - `per_device_train_batch_size: 16`每个设备（通常是 GPU）上的训练批大小。根据显存大小选择合适的 batch size。\n", "  - 启动任务：\n", "    - `.update` 触发任务的开始：模型先进行微调，微调完成后模型会部署起来，部署好后会自动使用评测集全部都过一遍推理以获得结果；\n", "  - 微调loss曲线\n", "   ![image-2.png](13_images/img24.png)\n", "- 模型评测：\n", "\n", "在共451题的测试集中，模型Qwen2.5-VL-3B-Instruct微调前后的精确匹配率和语义相似度如下所示：\n", "\n", "| Qwen2.5-VL-3B-Instruct | 微调前 | 微调后 |\n", "| ---------------------- | ------ | ------ |\n", "| 精确匹配率             | 0.00%  | 55.43% |\n", "| 语义相似度             | 31.85% | 80.64% |"]}, {"attachments": {}, "cell_type": "markdown", "id": "28bc46e0", "metadata": {}, "source": ["\n", "- 示例：针对如下影像，微调前后的输出\n", "\n", "![image.png](13_images/img26.png)\n", "\n", "微调前："]}, {"cell_type": "code", "execution_count": null, "id": "f9b3ffe8", "metadata": {"attributes": {"classes": ["JSON"], "id": ""}}, "outputs": [], "source": ["{\n", "        \"query\": \"is the liver visible in the image?\",\n", "        \"true\": \"no\",\n", "        \"infer\": \"yes, the liver is visible in the image. it appears as a large, dark gray structure located in the upper left quadrant of the abdomen.\",\n", "        \"exact_score\": 0,\n", "        \"cosine_score\": 0.3227266048281184\n", "}"]}, {"cell_type": "markdown", "id": "054c48b9", "metadata": {}, "source": ["微调后："]}, {"cell_type": "code", "execution_count": null, "id": "ddec24e6", "metadata": {"attributes": {"classes": ["JSON"], "id": ""}}, "outputs": [], "source": ["{\n", "        \"query\": \"is the liver visible in the image?\",\n", "        \"true\": \"no\",\n", "        \"infer\": \"no\",\n", "        \"exact_score\": 1,\n", "        \"cosine_score\": 1.0\n", "}"]}, {"attachments": {}, "cell_type": "markdown", "id": "1f0ff315", "metadata": {}, "source": ["### 拓展：ColPali 中的多模态 RAG\n", "\n", "前文介绍了对文档进行解析后对图像和文本进行分别处理的方法，那我们是否可以不对其进行解析和提取，直接将文档看作一个整体进行处理呢？目前有相关论文证实了这一想法的可能性。我们已经了解到多模态大模型（只讨论图像和文本两个模态）可以从图像和文本中提取信息并进行处理，如果我们将一页文档看作一张图像，就可以利用多模态大模型进行嵌入从而省去了文档解析的流程。然而，利用视觉多模态大模型对其进行嵌入和检索的相关方法有DSE、ColPali，M3DocRAG等，下文以ColPali为例，讲解与基于文档解析的方法之间的不同之处（图源ColPali论文）。\n", "\n", "![image.png](13_images/img27.png)\n", "\n", "ColPali（Contextualized Late Interaction over PaliGemma）使用PaliGemma（一个强大的多模态预训练模型）作为多模态编码器，对文档进行嵌入。具体来说将某一页文档看成n个图像块，每个块都对应一个向量，由此一来一页图片就可以用这n个向量来表示。在计算相似度时，ColPali不同于往常通过一个向量表示一个句子或一个段落的方式，而是利用嵌入列表，采用 \"MaxSim \"操作来计算两个文本之间的相似度。MaxSim 计算查询中的每个词与文档中一个页面对应的所有块之间的最大相似度，累加每个词与块的最大相似度得到该页面与查询的总相似度分数。对所有页面计算查询的总相似度分数，然后对页面按照总相似度分数降序排列，选择总相似度最高的页面作为与查询最匹配的页面。具体公式如下，其中S为查询q和文档d之间的最终相似度得分，Eq表示查询q的嵌入，Edi表示图像块的嵌入：\n", "\n", "$$\\begin{equation} \\text{S}_{q, d} = \\sum_{i \\in |E_q|} \\max_{j \\in |E_d|} E_{q_i} \\cdot E_{d_j}^T \\end{equation}$$\n", "\n", "ColPali有着相当出色的表现，并且展示了在多模态检索和生成方面的巨大潜力，但在实际应用中仍然面临一些挑战。例如当处理大量 PDF 文档时，其相似度计算的时间复杂度呈现出显著的增长，这在实际项目中可能导致性能瓶颈。此外，即使在多模态信息处理上具有创新性，但在文本检索任务中的效果仍然有进一步提升的空间，目前来看ColPali更适用于检索图表丰富的文档。ColPali 为多模态 RAG系统提供了一种全新的范式，在当前多模态大模型迅速发展的背景下，必然会涌现出更多功能更强大、效率更高的多模态解决方案，带来更加精准和高效的多模态信息处理方法，推动多模态 RAG 系统的发展，展现出更强的优势和实用性。\n", "\n", "## 参考文献\n", "\n", " MLLM：https://arxiv.org/pdf/2306.13549\n", "\n", "layoutlmv3 ：https://arxiv.org/abs/2204.08387\n", "\n", "VisRAG：https://arxiv.org/pdf/2410.10594\n", "\n", "ColPali：https://arxiv.org/pdf/2407.01449\n", "\n", "三阶段示例：https://www.eyelevel.ai/post/multimodal-rag-explained"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}