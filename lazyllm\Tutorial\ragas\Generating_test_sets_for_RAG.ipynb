{"cells": [{"cell_type": "markdown", "id": "10d22651", "metadata": {}, "source": ["# RAG 测试集生成教程\n", "\n", "本教程展示如何使用 Ragas 库为检索增强生成（RAG）系统生成高质量的测试数据集。\n", "\n", "## 主要内容：\n", "1. **文档加载**：从本地目录加载 Markdown 文档\n", "2. **模型配置**：配置大语言模型和嵌入模型\n", "3. **测试集生成**：使用 Ragas 自动生成问答对\n", "4. **知识图谱构建**：创建文档间的关系图谱\n", "5. **高级测试集生成**：基于知识图谱生成更复杂的测试用例\n", "\n", "## 参考链接:\n", "https://docs.ragas.org.cn/en/stable/getstarted/rag_testset_generation/#analyzing-the-testset"]}, {"cell_type": "markdown", "id": "doc_loading_section", "metadata": {}, "source": ["## 1. 文档加载\n", "\n", "使用 LangChain 的 DirectoryLoader 从指定目录加载所有 Markdown 文档。这些文档将作为生成测试集的知识库。"]}, {"cell_type": "code", "execution_count": 1, "id": "e2c273ef", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n", "short text: \"license: apache-2.0\". Defaulting to English.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["成功加载 12 个文档\n"]}], "source": ["# 导入 LangChain 的文档加载器\n", "from langchain_community.document_loaders import DirectoryLoader\n", "\n", "# 指定包含 Markdown 文档的目录路径\n", "path = \"Sample_Docs_Markdown/\"\n", "\n", "# 创建目录加载器，使用 glob 模式匹配所有 .md 文件（包括子目录）\n", "# \"**/*.md\" 表示递归搜索所有子目录中的 .md 文件\n", "loader = DirectoryLoader(path, glob=\"**/*.md\")\n", "\n", "# 加载所有文档，返回 Document 对象列表\n", "# 每个 Document 包含 page_content（文档内容）和 metadata（元数据）\n", "docs = loader.load()\n", "\n", "print(f\"成功加载 {len(docs)} 个文档\")"]}, {"cell_type": "markdown", "id": "llm_config_section", "metadata": {}, "source": ["## 2. 大语言模型配置\n", "\n", "配置用于生成测试集的大语言模型。这里使用阿里云的通义千问模型，通过 OpenAI 兼容接口调用。"]}, {"cell_type": "code", "execution_count": 2, "id": "41a75db5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["大语言模型配置完成\n"]}], "source": ["# 导入 LangChain 的 OpenAI 聊天模型\n", "from langchain_openai import ChatOpenAI\n", "\n", "# 配置大语言模型\n", "# 使用阿里云通义千问模型，通过 OpenAI 兼容接口调用\n", "llm = ChatOpenAI(\n", "    openai_api_base=\"https://dashscope.aliyuncs.com/compatible-mode/v1\",  # 阿里云 API 端点\n", "    openai_api_key=\"sk-282f3112bd714d6e85540da173b5517c\",  # API 密钥（实际使用时请替换为您的密钥）\n", "    model=\"qwen-plus-2025-01-25\"  # 模型名称\n", ")\n", "\n", "print(\"大语言模型配置完成\")"]}, {"cell_type": "markdown", "id": "model_wrapper_section", "metadata": {}, "source": ["## 3. 模型包装器配置\n", "\n", "将 LangChain 模型包装为 Ragas 兼容的格式，包括大语言模型和嵌入模型。"]}, {"cell_type": "code", "execution_count": 3, "id": "656a4348", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型包装器配置完成\n"]}], "source": ["# 以下是使用 OpenAI 模型的示例配置（已注释）\n", "# from ragas.llms import LangchainLLMWrapper\n", "# from ragas.embeddings import LangchainEmbeddingsWrapper\n", "# from langchain_openai import ChatOpenAI\n", "# from langchain_openai import OpenAIEmbeddings\n", "# generator_llm = LangchainLLMWrapper(ChatOpenAI(model=\"gpt-4o\"))\n", "# generator_embeddings = LangchainEmbeddingsWrapper(OpenAIEmbeddings())\n", "\n", "# 导入 Ragas 的模型包装器\n", "from ragas.llms import LangchainLLMWrapper\n", "from ragas.embeddings import LangchainEmbeddingsWrapper\n", "from jina_embeddings import create_jina_embeddings\n", "\n", "# 将 LangChain LLM 包装为 Ragas 兼容格式\n", "# 用于生成测试问题和答案\n", "generator_llm = LangchainLLMWrapper(llm)\n", "\n", "# 配置嵌入模型\n", "# 使用 Jina AI 的嵌入模型来计算文档和查询的向量表示\n", "# 嵌入模型用于文档相似性计算和检索\n", "generator_embeddings = LangchainEmbeddingsWrapper(\n", "    create_jina_embeddings(\n", "        api_key='jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR'  # Jina AI API 密钥\n", "    )\n", ")\n", "\n", "print(\"模型包装器配置完成\")"]}, {"cell_type": "markdown", "id": "testset_generation_section", "metadata": {}, "source": ["## 4. 基础测试集生成\n", "\n", "使用 Ragas 的 TestsetGenerator 直接从 LangChain 文档生成测试集。这是最简单的生成方式。"]}, {"cell_type": "code", "execution_count": 6, "id": "4d3cfc2d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 开始生成测试集...\n", "尝试使用标准方法生成测试集...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b85fb055c73c4cf99609a816f3d33b54", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying HeadlinesExtractor:   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7402eaa6fcec4f07884461a758ec85d8", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying HeadlineSplitter:   0%|          | 0/12 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3de2b0e979e64a7bafe5554427430143", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying SummaryExtractor:   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2da0a9a3c4de434cbc5a702873d41b26", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying CustomNodeFilter: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dcf1e163b66942b38d53557b1668d83b", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying [EmbeddingExtractor, ThemesExtractor, NERExtractor]:   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n", "unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n", "unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n", "unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n", "unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "608383403f624f139231f29d405300c4", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying [CosineSimilarityBuilder, OverlapScoreBuilder]:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: Node f6431972-9335-4661-81ec-ab72b1e28fab has no summary_embedding\n"]}, {"name": "stdout", "output_type": "stream", "text": ["❌ 标准方法失败: No nodes that satisfied the given filer. Try changing the filter.\n", "🔄 尝试使用备选方法...\n", "📊 手动创建知识图谱，包含 12 个节点\n", "✅ 基础转换完成\n", "❌ 备选方法也失败了: cannot import name 'SingleHopRandomQuerySynthesizer' from 'ragas.testset.synthesizers' (d:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\testset\\synthesizers\\__init__.py)\n", "🔧 尝试最简单的生成方法...\n", "✅ 使用简单方法生成了 10 个基础样本\n", "\n", "📊 最终结果: 成功生成包含 10 个样本的测试集\n"]}], "source": ["# 导入 Ragas 测试集生成器\n", "from ragas.testset import TestsetGenerator\n", "from ragas.testset.transforms import default_transforms, apply_transforms\n", "from ragas.testset.graph import KnowledgeGraph, Node, NodeType\n", "\n", "# 方法1：使用改进的生成方式（推荐）\n", "def generate_testset_robust(docs, testset_size=10):\n", "    \"\"\"\n", "    更稳健的测试集生成方法，包含错误处理和备选方案\n", "    \"\"\"\n", "    try:\n", "        # 创建测试集生成器\n", "        generator = TestsetGenerator(\n", "            llm=generator_llm,\n", "            embedding_model=generator_embeddings\n", "        )\n", "        \n", "        # 尝试标准生成方法\n", "        print(\"尝试使用标准方法生成测试集...\")\n", "        dataset = generator.generate_with_langchain_docs(\n", "            docs,\n", "            testset_size=testset_size\n", "        )\n", "        print(f\"✅ 成功生成包含 {len(dataset)} 个样本的测试集\")\n", "        return dataset\n", "        \n", "    except ValueError as e:\n", "        if \"No nodes that satisfied the given filer\" in str(e):\n", "            print(f\"❌ 标准方法失败: {e}\")\n", "            print(\"🔄 尝试使用备选方法...\")\n", "            \n", "            # 备选方法：手动构建知识图谱\n", "            return generate_testset_manual_kg(docs, testset_size)\n", "        else:\n", "            raise e\n", "\n", "def generate_testset_manual_kg(docs, testset_size=10):\n", "    \"\"\"\n", "    手动构建知识图谱的备选方法\n", "    \"\"\"\n", "    try:\n", "        # 创建知识图谱\n", "        kg = KnowledgeGraph()\n", "        \n", "        # 手动添加文档节点，确保包含必要属性\n", "        for i, doc in enumerate(docs):\n", "            # 生成简单摘要（如果文档内容过长）\n", "            content = doc.page_content\n", "            summary = content[:200] + \"...\" if len(content) > 200 else content\n", "            \n", "            node = Node(\n", "                type=NodeType.DOCUMENT,\n", "                properties={\n", "                    \"page_content\": content,\n", "                    \"document_metadata\": doc.metadata,\n", "                    \"summary\": summary,  # 确保有摘要\n", "                    \"doc_id\": f\"doc_{i}\",\n", "                    \"themes\": [\"general\"],  # 添加默认主题\n", "                }\n", "            )\n", "            kg.nodes.append(node)\n", "        \n", "        print(f\"📊 手动创建知识图谱，包含 {len(kg.nodes)} 个节点\")\n", "        \n", "        # 应用基础转换（跳过可能失败的转换）\n", "        try:\n", "            # 只应用嵌入提取，跳过其他可能失败的转换\n", "            from ragas.testset.transforms import EmbeddingExtractor\n", "            embedding_extractor = EmbeddingExtractor(embedding_model=generator_embeddings)\n", "            \n", "            # 为每个节点添加嵌入\n", "            for node in kg.nodes:\n", "                if \"summary\" in node.properties:\n", "                    # 这里可以添加嵌入计算，但为了简化先跳过\n", "                    pass\n", "            \n", "            print(\"✅ 基础转换完成\")\n", "        except Exception as transform_error:\n", "            print(f\"⚠️ 转换过程中出现警告: {transform_error}\")\n", "            print(\"继续使用基础知识图谱...\")\n", "        \n", "        # 创建生成器并使用手动构建的知识图谱\n", "        generator = TestsetGenerator(\n", "            llm=generator_llm,\n", "            embedding_model=generator_embeddings,\n", "            knowledge_graph=kg\n", "        )\n", "        \n", "        # 使用简化的查询分布\n", "        from ragas.testset.synthesizers import (\n", "            SingleHopSpecificQuerySynthesizer,\n", "            SingleHopRandomQuerySynthesizer\n", "        )\n", "        \n", "        # 只使用单跳查询，避免复杂的多跳查询\n", "        simple_distribution = {\n", "            SingleHopSpecificQuerySynthesizer(llm=generator_llm): 0.7,\n", "            SingleHopRandomQuerySynthesizer(llm=generator_llm): 0.3,\n", "        }\n", "        \n", "        print(\"🎯 使用简化的查询分布生成测试集...\")\n", "        dataset = generator.generate(\n", "            testset_size=testset_size,\n", "            query_distribution=simple_distribution\n", "        )\n", "        \n", "        print(f\"✅ 成功生成包含 {len(dataset)} 个样本的测试集\")\n", "        return dataset\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 备选方法也失败了: {e}\")\n", "        print(\"🔧 尝试最简单的生成方法...\")\n", "        return generate_testset_simple(docs, testset_size)\n", "\n", "def generate_testset_simple(docs, testset_size=10):\n", "    \"\"\"\n", "    最简单的测试集生成方法\n", "    \"\"\"\n", "    from ragas.testset.synthesizers import SingleHopSpecificQuerySynthesizer\n", "    \n", "    # 创建最基础的知识图谱\n", "    kg = KnowledgeGraph()\n", "    \n", "    for i, doc in enumerate(docs[:5]):  # 只使用前5个文档\n", "        node = Node(\n", "            type=NodeType.DOCUMENT,\n", "            properties={\n", "                \"page_content\": doc.page_content,\n", "                \"summary\": doc.page_content[:100],  # 简单截取作为摘要\n", "                \"themes\": [\"document\"],\n", "                \"doc_id\": f\"simple_doc_{i}\"\n", "            }\n", "        )\n", "        kg.nodes.append(node)\n", "    \n", "    # 使用最简单的生成器\n", "    synthesizer = SingleHopSpecificQuerySynthesizer(llm=generator_llm)\n", "    \n", "    # 手动生成一些基础样本\n", "    samples = []\n", "    for i, doc in enumerate(docs[:min(testset_size, len(docs))]):\n", "        sample = {\n", "            \"user_input\": f\"What is the main content of document {i+1}?\",\n", "            \"reference_contexts\": [doc.page_content[:500]],\n", "            \"reference\": f\"The document discusses: {doc.page_content[:200]}...\",\n", "            \"synthesizer_name\": \"simple_manual_synthesizer\"\n", "        }\n", "        samples.append(sample)\n", "    \n", "    print(f\"✅ 使用简单方法生成了 {len(samples)} 个基础样本\")\n", "    \n", "    # 转换为 Ragas 数据集格式\n", "    from ragas.testset import Testset\n", "    import pandas as pd\n", "    \n", "    df = pd.DataFrame(samples)\n", "    dataset = Testset.from_pandas(df)\n", "    \n", "    return dataset\n", "\n", "# 执行稳健的测试集生成\n", "print(\"🚀 开始生成测试集...\")\n", "dataset = generate_testset_robust(docs, testset_size=10)\n", "\n", "print(f\"\\n📊 最终结果: 成功生成包含 {len(dataset)} 个样本的测试集\")"]}, {"cell_type": "code", "execution_count": 7, "id": "3735f164", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "66f2daaa0a604e449b56151e1392066b", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying HeadlinesExtractor:   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1b75ddfcd43f46a3a5357fcfff3abb5c", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying HeadlineSplitter:   0%|          | 0/12 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "92f5681d2279437faeee9e27c4ceab75", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying SummaryExtractor:   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n", "unable to apply transformation: Connection error.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2a425d096ce144d0973dddaf794a9fc2", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying CustomNodeFilter: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ef51f556cf2b45b8bbb8644e27eec963", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying [EmbeddingExtractor, ThemesExtractor, NERExtractor]:   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n", "unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n", "unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n", "unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n", "unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ac815e7948344241a89c5b773ff29179", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying [CosineSimilarityBuilder, OverlapScoreBuilder]:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: Node f51e1f76-5479-4f6c-9eef-315cb2994392 has no summary_embedding\n"]}, {"ename": "ValueError", "evalue": "No nodes that satisfied the given filer. Try changing the filter.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 5\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mragas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtestset\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m TestsetGenerator\n\u001b[0;32m      4\u001b[0m generator \u001b[38;5;241m=\u001b[39m TestsetGenerator(llm\u001b[38;5;241m=\u001b[39mgenerator_llm, embedding_model\u001b[38;5;241m=\u001b[39mgenerator_embeddings)\n\u001b[1;32m----> 5\u001b[0m dataset \u001b[38;5;241m=\u001b[39m \u001b[43mgenerator\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate_with_langchain_docs\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdocs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtestset_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\testset\\synthesizers\\generate.py:188\u001b[0m, in \u001b[0;36mTestsetGenerator.generate_with_langchain_docs\u001b[1;34m(self, documents, testset_size, transforms, transforms_llm, transforms_embedding_model, query_distribution, run_config, callbacks, with_debugging_logs, raise_exceptions)\u001b[0m\n\u001b[0;32m    185\u001b[0m apply_transforms(kg, transforms)\n\u001b[0;32m    186\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mknowledge_graph \u001b[38;5;241m=\u001b[39m kg\n\u001b[1;32m--> 188\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    189\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtestset_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtestset_size\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    190\u001b[0m \u001b[43m    \u001b[49m\u001b[43mquery_distribution\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquery_distribution\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    191\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrun_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    192\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    193\u001b[0m \u001b[43m    \u001b[49m\u001b[43mwith_debugging_logs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mwith_debugging_logs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    194\u001b[0m \u001b[43m    \u001b[49m\u001b[43mraise_exceptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mraise_exceptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    195\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\testset\\synthesizers\\generate.py:369\u001b[0m, in \u001b[0;36mTestsetGenerator.generate\u001b[1;34m(self, testset_size, query_distribution, num_personas, run_config, batch_size, callbacks, token_usage_parser, with_debugging_logs, raise_exceptions)\u001b[0m\n\u001b[0;32m    366\u001b[0m     patch_logger(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mragas.experimental.testset.transforms\u001b[39m\u001b[38;5;124m\"\u001b[39m, logging\u001b[38;5;241m.\u001b[39mDEBUG)\n\u001b[0;32m    368\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpersona_list \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m--> 369\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpersona_list \u001b[38;5;241m=\u001b[39m \u001b[43mgenerate_personas_from_kg\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    370\u001b[0m \u001b[43m        \u001b[49m\u001b[43mllm\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mllm\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    371\u001b[0m \u001b[43m        \u001b[49m\u001b[43mkg\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mknowledge_graph\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    372\u001b[0m \u001b[43m        \u001b[49m\u001b[43mnum_personas\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnum_personas\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    373\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    374\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    375\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    376\u001b[0m     random\u001b[38;5;241m.\u001b[39mshuffle(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpersona_list)\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\testset\\persona.py:95\u001b[0m, in \u001b[0;36mgenerate_personas_from_kg\u001b[1;34m(kg, llm, persona_generation_prompt, num_personas, filter_fn, callbacks)\u001b[0m\n\u001b[0;32m     93\u001b[0m nodes \u001b[38;5;241m=\u001b[39m [node \u001b[38;5;28;01mfor\u001b[39;00m node \u001b[38;5;129;01min\u001b[39;00m kg\u001b[38;5;241m.\u001b[39mnodes \u001b[38;5;28;01mif\u001b[39;00m filter_fn(node)]\n\u001b[0;32m     94\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(nodes) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m---> 95\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m     96\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo nodes that satisfied the given filer. Try changing the filter.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     97\u001b[0m     )\n\u001b[0;32m     99\u001b[0m summaries \u001b[38;5;241m=\u001b[39m [node\u001b[38;5;241m.\u001b[39mproperties\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msummary\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m node \u001b[38;5;129;01min\u001b[39;00m nodes]\n\u001b[0;32m    100\u001b[0m summaries \u001b[38;5;241m=\u001b[39m [summary \u001b[38;5;28;01mfor\u001b[39;00m summary \u001b[38;5;129;01min\u001b[39;00m summaries \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(summary, \u001b[38;5;28mstr\u001b[39m)]\n", "\u001b[1;31mValueError\u001b[0m: No nodes that satisfied the given filer. Try changing the filter."]}], "source": ["# 上面的代码的第二版本\n", "from ragas.testset import TestsetGenerator\n", "\n", "generator = TestsetGenerator(llm=generator_llm, embedding_model=generator_embeddings)\n", "dataset = generator.generate_with_langchain_docs(docs, testset_size=10)"]}, {"cell_type": "markdown", "id": "error_solution_section", "metadata": {}, "source": ["## 🔧 错误解决方案说明\n", "\n", "上面的代码提供了三层错误处理机制：\n", "\n", "### 1. **标准方法**\n", "- 首先尝试使用 `generate_with_langchain_docs()` 的标准方法\n", "- 如果成功，直接返回结果\n", "\n", "### 2. **备选方法**\n", "- 如果标准方法失败，手动构建知识图谱\n", "- 确保每个节点都有必要的属性（summary, themes 等）\n", "- 使用简化的查询分布，避免复杂的多跳查询\n", "\n", "### 3. **简单方法**\n", "- 如果备选方法也失败，使用最基础的生成方式\n", "- 手动创建简单的问答对\n", "- 确保至少能生成一些基础的测试样本\n", "\n", "### 常见错误原因\n", "1. **文档质量问题**：文档内容过短或格式不规范\n", "2. **模型配置问题**：LLM 或嵌入模型配置不正确\n", "3. **依赖版本问题**：Ragas 版本与其他库不兼容\n", "4. **资源限制**：内存不足或 API 调用限制"]}, {"cell_type": "markdown", "id": "quick_fix_section", "metadata": {}, "source": ["## ⚡ 快速修复方案\n", "\n", "如果您想要一个更简单直接的解决方案，可以尝试以下代码："]}, {"cell_type": "code", "execution_count": 8, "id": "quick_fix_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["尝试生成较少的样本...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "547b8ebabab74f7ab8efdc4cce4711e1", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying HeadlinesExtractor:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: Connection error.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5f42d5e2c1cc4744a686353b8af81053", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying HeadlineSplitter:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8cbd0ed068fa4b528c12324aaa511456", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying SummaryExtractor:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: Connection error.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6c6237c980614593943eb1240d443c48", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying CustomNodeFilter: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c1f57ba54a9c44808bdcd5822980c248", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying [EmbeddingExtractor, ThemesExtractor, NERExtractor]:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: node.property('summary') must be a string, found '<class 'NoneType'>'\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "65b6987003e244e1b9b4b4c6a3c990b8", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying [CosineSimilarityBuilder, OverlapScoreBuilder]:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: Node 4fa15f5e-6032-4064-bb1e-601cdb406d23 has no summary_embedding\n"]}, {"name": "stdout", "output_type": "stream", "text": ["快速修复也失败了: No nodes that satisfied the given filer. Try changing the filter.\n", "\n", "建议检查：\n", "1. 文档内容是否足够长且有意义\n", "2. LLM 和嵌入模型是否正确配置\n", "3. 网络连接是否正常\n", "4. API 密钥是否有效\n"]}], "source": ["# 快速修复：使用最小化配置\n", "try:\n", "    # 方法1：减少样本数量\n", "    print(\"尝试生成较少的样本...\")\n", "    generator_simple = TestsetGenerator(\n", "        llm=generator_llm,\n", "        embedding_model=generator_embeddings\n", "    )\n", "    \n", "    # 只使用前3个文档，生成5个样本\n", "    dataset = generator_simple.generate_with_langchain_docs(\n", "        docs[:3],  # 只使用前3个文档\n", "        testset_size=5  # 减少样本数量\n", "    )\n", "    print(f\"✅ 成功生成 {len(dataset)} 个样本\")\n", "    \n", "except Exception as e:\n", "    print(f\"快速修复也失败了: {e}\")\n", "    print(\"\\n建议检查：\")\n", "    print(\"1. 文档内容是否足够长且有意义\")\n", "    print(\"2. LLM 和嵌入模型是否正确配置\")\n", "    print(\"3. 网络连接是否正常\")\n", "    print(\"4. API 密钥是否有效\")"]}, {"cell_type": "markdown", "id": "dataset_display_section", "metadata": {}, "source": ["## 5. 查看生成的测试集\n", "\n", "将生成的测试集转换为 Pandas DataFrame 格式，便于查看和分析。"]}, {"cell_type": "code", "execution_count": null, "id": "18d51456", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_input</th>\n", "      <th>reference_contexts</th>\n", "      <th>reference</th>\n", "      <th>synthesizer_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>How does GitLab promote inclusion and allyship...</td>\n", "      <td>[title: \"The Ally Lab\" description: Learn what...</td>\n", "      <td>GitLab promotes inclusion by requiring all tea...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>In the context of allyship, how can an individ...</td>\n", "      <td>[Skills and Behaviors of allies To be an effec...</td>\n", "      <td>An effective ally would actively listen to the...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Whot is the impact of perfromative allyship on...</td>\n", "      <td>[Concepts &amp; Terms Privilege: an unearned advan...</td>\n", "      <td>Performative allyship refers to actions taken ...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>What iz the purpos of CulturAmp in evluating a...</td>\n", "      <td>[title: \"Building an Inclusive Remote Culture\"...</td>\n", "      <td>CultureAmp is used to conduct an annual survey...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Whot is the roel of divrsity in crating an inc...</td>\n", "      <td>[Creating a Diversity, Inclusion &amp; Belonging A...</td>\n", "      <td>Diversity plays a crucial role in creating an ...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>What are the key components of the Sponsorship...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\ntitle: \"Sales Sponsorship Pilot Pr...</td>\n", "      <td>The Sponsorship Program at GitLab is designed ...</td>\n", "      <td>multi_hop_specific_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>What are the key skills and behaviors of allie...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\nSkills and Behaviors of allies To ...</td>\n", "      <td>The key skills and behaviors of allies include...</td>\n", "      <td>multi_hop_specific_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>What is the purpose of the Leadership DIB Coun...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\nDIB in Q2 - PBPs Only To ensure st...</td>\n", "      <td>The Leadership DIB Council Call serves as a me...</td>\n", "      <td>multi_hop_specific_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Wht ar th bnfits f a successful spnsorship prg...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\ntitle: \"Sales Sponsorship Pilot Pr...</td>\n", "      <td>A successful sponsorship program provides seve...</td>\n", "      <td>multi_hop_specific_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>In the context of the Sponsorship Program, how...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\nWhat does a successful sponsorship...</td>\n", "      <td>The development phase of the relationship in t...</td>\n", "      <td>multi_hop_specific_query_synthesizer</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          user_input  \\\n", "0  How does GitLab promote inclusion and allyship...   \n", "1  In the context of allyship, how can an individ...   \n", "2  Whot is the impact of perfromative allyship on...   \n", "3  What iz the purpos of CulturAmp in evluating a...   \n", "4  Whot is the roel of divrsity in crating an inc...   \n", "5  What are the key components of the Sponsorship...   \n", "6  What are the key skills and behaviors of allie...   \n", "7  What is the purpose of the Leadership DIB Coun...   \n", "8  Wht ar th bnfits f a successful spnsorship prg...   \n", "9  In the context of the Sponsorship Program, how...   \n", "\n", "                                  reference_contexts  \\\n", "0  [title: \"The Ally Lab\" description: Learn what...   \n", "1  [Skills and Behaviors of allies To be an effec...   \n", "2  [Concepts & Terms Privilege: an unearned advan...   \n", "3  [title: \"Building an Inclusive Remote Culture\"...   \n", "4  [Creating a Diversity, Inclusion & Belonging A...   \n", "5  [<1-hop>\\n\\ntitle: \"Sales Sponsorship Pilot Pr...   \n", "6  [<1-hop>\\n\\nSkills and Behaviors of allies To ...   \n", "7  [<1-hop>\\n\\nDIB in Q2 - PBPs Only To ensure st...   \n", "8  [<1-hop>\\n\\ntitle: \"Sales Sponsorship Pilot Pr...   \n", "9  [<1-hop>\\n\\nWhat does a successful sponsorship...   \n", "\n", "                                           reference  \\\n", "0  GitLab promotes inclusion by requiring all tea...   \n", "1  An effective ally would actively listen to the...   \n", "2  Performative allyship refers to actions taken ...   \n", "3  CultureAmp is used to conduct an annual survey...   \n", "4  Diversity plays a crucial role in creating an ...   \n", "5  The Sponsorship Program at GitLab is designed ...   \n", "6  The key skills and behaviors of allies include...   \n", "7  The Leadership DIB Council Call serves as a me...   \n", "8  A successful sponsorship program provides seve...   \n", "9  The development phase of the relationship in t...   \n", "\n", "                       synthesizer_name  \n", "0  single_hop_specifc_query_synthesizer  \n", "1  single_hop_specifc_query_synthesizer  \n", "2  single_hop_specifc_query_synthesizer  \n", "3  single_hop_specifc_query_synthesizer  \n", "4  single_hop_specifc_query_synthesizer  \n", "5  multi_hop_specific_query_synthesizer  \n", "6  multi_hop_specific_query_synthesizer  \n", "7  multi_hop_specific_query_synthesizer  \n", "8  multi_hop_specific_query_synthesizer  \n", "9  multi_hop_specific_query_synthesizer  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将测试集转换为 Pandas DataFrame 格式进行查看\n", "# 测试集包含以下列：\n", "# - user_input: 用户问题/查询\n", "# - reference_contexts: 相关的参考上下文（从文档中提取）\n", "# - reference: 标准答案/参考答案\n", "# - synthesizer_name: 生成器类型（单跳或多跳查询合成器）\n", "\n", "df = dataset.to_pandas()\n", "print(f\"测试集包含 {len(df)} 个样本\")\n", "print(f\"其中单跳查询: {len(df[df['synthesizer_name'] == 'single_hop_specifc_query_synthesizer'])} 个\")\n", "print(f\"多跳查询: {len(df[df['synthesizer_name'] == 'multi_hop_specific_query_synthesizer'])} 个\")\n", "\n", "# 显示测试集内容\n", "df"]}, {"cell_type": "markdown", "id": "9c536978", "metadata": {}, "source": ["# 知识图谱创建\n", "\n", "知识图谱是一种更高级的文档表示方法，它能够捕获文档之间的复杂关系，从而生成更具挑战性的测试用例。\n", "\n", "## 知识图谱的优势：\n", "1. **关系建模**：捕获文档间的语义关系\n", "2. **多跳推理**：支持需要跨多个文档推理的复杂问题\n", "3. **结构化表示**：提供更丰富的文档结构信息\n", "4. **高质量测试**：生成更贴近真实使用场景的测试用例"]}, {"cell_type": "markdown", "id": "kg_init_section", "metadata": {}, "source": ["## 6. 初始化知识图谱\n", "\n", "创建一个空的知识图谱对象，用于存储文档节点和它们之间的关系。"]}, {"cell_type": "code", "execution_count": null, "id": "e5fb68b7", "metadata": {}, "outputs": [], "source": ["# 导入知识图谱相关类\n", "from ragas.testset.graph import KnowledgeGraph\n", "\n", "# 创建空的知识图谱实例\n", "# 知识图谱将用于存储文档节点和它们之间的关系\n", "kg = KnowledgeGraph()\n", "\n", "print(\"知识图谱初始化完成\")"]}, {"cell_type": "markdown", "id": "kg_nodes_section", "metadata": {}, "source": ["## 7. 添加文档节点\n", "\n", "将加载的文档转换为知识图谱中的节点。每个文档成为图中的一个节点，包含文档内容和元数据。"]}, {"cell_type": "code", "execution_count": null, "id": "4829d75d", "metadata": {}, "outputs": [], "source": ["# 导入节点相关类\n", "from ragas.testset.graph import Node, NodeType\n", "\n", "# 遍历所有加载的文档，为每个文档创建一个图节点\n", "for doc in docs:\n", "    # 创建文档类型的节点\n", "    node = Node(\n", "        type=NodeType.DOCUMENT,  # 节点类型：文档\n", "        properties={\n", "            \"page_content\": doc.page_content,  # 文档内容\n", "            \"document_metadata\": doc.metadata   # 文档元数据（如文件路径、标题等）\n", "        }\n", "    )\n", "    \n", "    # 将节点添加到知识图谱中\n", "    kg.nodes.append(node)\n", "\n", "print(f\"成功添加 {len(kg.nodes)} 个文档节点到知识图谱\")"]}, {"cell_type": "markdown", "id": "kg_transforms_section", "metadata": {}, "source": ["## 8. 应用知识图谱转换\n", "\n", "对知识图谱应用一系列转换操作，以丰富节点信息和建立节点间的关系。这些转换包括：\n", "- **标题提取**：提取文档标题和结构信息\n", "- **摘要生成**：为每个文档生成摘要\n", "- **主题提取**：识别文档的主要主题\n", "- **命名实体识别**：提取人名、地名、组织名等实体\n", "- **嵌入计算**：计算文档的向量表示\n", "- **相似性计算**：计算文档间的相似性关系"]}, {"cell_type": "code", "execution_count": null, "id": "81ce647a", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "237790f87a804421a284962c6aeadfc4", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying HeadlinesExtractor:   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "51810e8ce1764949aadebb4008c966a7", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying HeadlineSplitter:   0%|          | 0/12 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5b7d09f958874116ae05a3cb5568b745", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying SummaryExtractor:   0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Property 'summary' already exists in node 'e5fb37'. Skipping!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9737fad382594254ad5087148100aa3f", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying CustomNodeFilter:   0%|          | 0/12 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7bb497570dcf4fa2a2efaded0276cf05", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying [EmbeddingExtractor, ThemesExtractor, NERExtractor]:   0%|          | 0/30 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["异步嵌入请求失败 (尝试 1/3): \n", "Property 'summary_embedding' already exists in node 'e5fb37'. Skipping!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "19c1cd1117eb40fe9aae311e41c2ef56", "version_major": 2, "version_minor": 0}, "text/plain": ["Applying [CosineSimilarityBuilder, OverlapScoreBuilder]:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 导入转换相关模块\n", "from ragas.testset.transforms import default_transforms, apply_transforms\n", "\n", "# 配置转换所需的模型\n", "# 使用与测试集生成相同的 LLM 和嵌入模型以保持一致性\n", "transformer_llm = generator_llm      # 用于文本生成任务（如摘要生成）\n", "embedding_model = generator_embeddings  # 用于向量嵌入计算\n", "\n", "# 创建默认的转换管道\n", "# 这个管道包含了多个转换步骤，用于丰富知识图谱的信息\n", "trans = default_transforms(\n", "    documents=docs,                    # 原始文档\n", "    llm=transformer_llm,              # 大语言模型\n", "    embedding_model=embedding_model    # 嵌入模型\n", ")\n", "\n", "# 将转换应用到知识图谱\n", "# 这个过程会：\n", "# 1. 提取文档标题和结构\n", "# 2. 生成文档摘要\n", "# 3. 提取主题和命名实体\n", "# 4. 计算文档嵌入\n", "# 5. 建立文档间的相似性关系\n", "apply_transforms(kg, trans)\n", "\n", "print(\"知识图谱转换完成\")"]}, {"cell_type": "markdown", "id": "kg_save_section", "metadata": {}, "source": ["## 9. 保存和加载知识图谱\n", "\n", "将构建好的知识图谱保存到文件，以便后续使用或分享。"]}, {"cell_type": "code", "execution_count": null, "id": "cfe6f37e", "metadata": {}, "outputs": [{"data": {"text/plain": ["KnowledgeGraph(nodes: 25, relationships: 30)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将知识图谱保存到 JSON 文件\n", "# 这样可以避免重复构建，提高效率\n", "kg.save(\"knowledge_graph.json\")\n", "print(\"知识图谱已保存到 knowledge_graph.json\")\n", "\n", "# 从文件加载知识图谱\n", "# 演示如何重新加载已保存的知识图谱\n", "loaded_kg = KnowledgeGraph.load(\"knowledge_graph.json\")\n", "print(f\"成功加载知识图谱：{loaded_kg.nodes} 个节点，{loaded_kg.relationships} 个关系\")\n", "\n", "# 显示知识图谱的基本信息\n", "loaded_kg"]}, {"cell_type": "markdown", "id": "568075fb", "metadata": {}, "source": ["# 基于知识图谱的高级测试集生成\n", "\n", "使用构建好的知识图谱来生成更高质量的测试集。基于知识图谱的生成方式能够：\n", "- 利用文档间的关系信息\n", "- 生成需要多文档推理的复杂问题\n", "- 提供更丰富的上下文信息\n", "- 创建更贴近实际使用场景的测试用例"]}, {"cell_type": "markdown", "id": "advanced_generator_section", "metadata": {}, "source": ["## 10. 创建基于知识图谱的生成器\n", "\n", "使用知识图谱创建一个新的测试集生成器，这个生成器能够利用图结构信息。"]}, {"cell_type": "code", "execution_count": null, "id": "b95b20b3", "metadata": {}, "outputs": [], "source": ["# 导入测试集生成器（如果之前没有导入）\n", "from ragas.testset import TestsetGenerator\n", "\n", "# 创建基于知识图谱的测试集生成器\n", "# 这个生成器能够利用知识图谱中的结构信息和关系\n", "generator = TestsetGenerator(\n", "    llm=generator_llm,              # 大语言模型\n", "    embedding_model=embedding_model, # 嵌入模型\n", "    knowledge_graph=loaded_kg       # 知识图谱（关键差异）\n", ")\n", "\n", "print(\"基于知识图谱的测试集生成器创建完成\")"]}, {"cell_type": "markdown", "id": "query_distribution_section", "metadata": {}, "source": ["## 11. 配置查询分布\n", "\n", "定义生成的测试问题的类型分布。Ragas 支持多种查询类型，包括简单查询、推理查询、多跳查询等。"]}, {"cell_type": "code", "execution_count": null, "id": "32e46ff8", "metadata": {}, "outputs": [], "source": ["# 导入查询分布配置\n", "from ragas.testset.synthesizers import default_query_distribution\n", "\n", "# 创建默认的查询分布配置\n", "# 这定义了不同类型查询的生成比例：\n", "# - 简单查询：直接从单个文档中可以回答的问题\n", "# - 推理查询：需要一定推理能力的问题\n", "# - 多跳查询：需要跨多个文档推理的复杂问题\n", "# - 条件查询：包含条件判断的问题\n", "query_distribution = default_query_distribution(generator_llm)\n", "\n", "print(\"查询分布配置完成\")\n", "print(\"支持的查询类型：简单查询、推理查询、多跳查询、条件查询\")"]}, {"cell_type": "markdown", "id": "advanced_generation_section", "metadata": {}, "source": ["## 12. 生成基于知识图谱的测试集\n", "\n", "使用知识图谱和配置好的查询分布来生成高质量的测试集。\n", "\n", "**注意**：这个单元格可能会遇到错误，因为知识图谱中可能没有足够的聚类信息。这是正常现象，说明当前的文档集合相对较小或者文档间的关系不够密切。"]}, {"cell_type": "code", "execution_count": null, "id": "93183adf", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2f0b83cc04724892aceb2574257f3210", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating Scenarios:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "ValueError", "evalue": "No clusters found in the knowledge graph. Try changing the relationship condition.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[16], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m testset \u001b[38;5;241m=\u001b[39m \u001b[43mgenerator\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtestset_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mquery_distribution\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquery_distribution\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      2\u001b[0m testset\u001b[38;5;241m.\u001b[39mto_pandas()\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\testset\\synthesizers\\generate.py:413\u001b[0m, in \u001b[0;36mTestsetGenerator.generate\u001b[1;34m(self, testset_size, query_distribution, num_personas, run_config, batch_size, callbacks, token_usage_parser, with_debugging_logs, raise_exceptions)\u001b[0m\n\u001b[0;32m    411\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    412\u001b[0m     scenario_generation_rm\u001b[38;5;241m.\u001b[39mon_chain_error(e)\n\u001b[1;32m--> 413\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[0;32m    414\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    415\u001b[0m     scenario_generation_rm\u001b[38;5;241m.\u001b[39mon_chain_end(\n\u001b[0;32m    416\u001b[0m         outputs\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mscenario_sample_list\u001b[39m\u001b[38;5;124m\"\u001b[39m: scenario_sample_list}\n\u001b[0;32m    417\u001b[0m     )\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\testset\\synthesizers\\generate.py:410\u001b[0m, in \u001b[0;36mTestsetGenerator.generate\u001b[1;34m(self, testset_size, query_distribution, num_personas, run_config, batch_size, callbacks, token_usage_parser, with_debugging_logs, raise_exceptions)\u001b[0m\n\u001b[0;32m    401\u001b[0m     exec\u001b[38;5;241m.\u001b[39msubmit(\n\u001b[0;32m    402\u001b[0m         scenario\u001b[38;5;241m.\u001b[39mgenerate_scenarios,\n\u001b[0;32m    403\u001b[0m         n\u001b[38;5;241m=\u001b[39msplits[i],\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    406\u001b[0m         callbacks\u001b[38;5;241m=\u001b[39mscenario_generation_grp,\n\u001b[0;32m    407\u001b[0m     )\n\u001b[0;32m    409\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 410\u001b[0m     scenario_sample_list: t\u001b[38;5;241m.\u001b[39mList[t\u001b[38;5;241m.\u001b[39mList[BaseScenario]] \u001b[38;5;241m=\u001b[39m \u001b[43mexec\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mresults\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    411\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    412\u001b[0m     scenario_generation_rm\u001b[38;5;241m.\u001b[39mon_chain_error(e)\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\executor.py:213\u001b[0m, in \u001b[0;36mExecutor.results\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    210\u001b[0m             nest_asyncio\u001b[38;5;241m.\u001b[39mapply()\n\u001b[0;32m    211\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_nest_asyncio_applied \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m--> 213\u001b[0m results \u001b[38;5;241m=\u001b[39m \u001b[43masyncio\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_process_jobs\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    214\u001b[0m sorted_results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28msorted\u001b[39m(results, key\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mlambda\u001b[39;00m x: x[\u001b[38;5;241m0\u001b[39m])\n\u001b[0;32m    215\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m [r[\u001b[38;5;241m1\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m r \u001b[38;5;129;01min\u001b[39;00m sorted_results]\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\nest_asyncio.py:30\u001b[0m, in \u001b[0;36m_patch_asyncio.<locals>.run\u001b[1;34m(main, debug)\u001b[0m\n\u001b[0;32m     28\u001b[0m task \u001b[38;5;241m=\u001b[39m asyncio\u001b[38;5;241m.\u001b[39mensure_future(main)\n\u001b[0;32m     29\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 30\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mloop\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_until_complete\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtask\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     31\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[0;32m     32\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m task\u001b[38;5;241m.\u001b[39mdone():\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\nest_asyncio.py:98\u001b[0m, in \u001b[0;36m_patch_loop.<locals>.run_until_complete\u001b[1;34m(self, future)\u001b[0m\n\u001b[0;32m     95\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m f\u001b[38;5;241m.\u001b[39mdone():\n\u001b[0;32m     96\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[0;32m     97\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mEvent loop stopped before Future completed.\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m---> 98\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mresult\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\asyncio\\futures.py:201\u001b[0m, in \u001b[0;36mFuture.result\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    199\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__log_traceback \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mF<PERSON>e\u001b[39;00m\n\u001b[0;32m    200\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m--> 201\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception\u001b[38;5;241m.\u001b[39mwith_traceback(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception_tb)\n\u001b[0;32m    202\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\asyncio\\tasks.py:232\u001b[0m, in \u001b[0;36mTask.__step\u001b[1;34m(***failed resolving arguments***)\u001b[0m\n\u001b[0;32m    228\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    229\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m exc \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    230\u001b[0m         \u001b[38;5;66;03m# We use the `send` method directly, because coroutines\u001b[39;00m\n\u001b[0;32m    231\u001b[0m         \u001b[38;5;66;03m# don't have `__iter__` and `__next__` methods.\u001b[39;00m\n\u001b[1;32m--> 232\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[43mcoro\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[0;32m    233\u001b[0m     \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[0;32m    234\u001b[0m         result \u001b[38;5;241m=\u001b[39m coro\u001b[38;5;241m.\u001b[39mthrow(exc)\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\executor.py:141\u001b[0m, in \u001b[0;36mExecutor._process_jobs\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    135\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpbar \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    136\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m tqdm(\n\u001b[0;32m    137\u001b[0m         total\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mjobs),\n\u001b[0;32m    138\u001b[0m         desc\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdesc,\n\u001b[0;32m    139\u001b[0m         disable\u001b[38;5;241m=\u001b[39m\u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mshow_progress,\n\u001b[0;32m    140\u001b[0m     ) \u001b[38;5;28;01mas\u001b[39;00m internal_pbar:\n\u001b[1;32m--> 141\u001b[0m         \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_coroutines(\n\u001b[0;32m    142\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mjobs, internal_pbar, results, max_workers\n\u001b[0;32m    143\u001b[0m         )\n\u001b[0;32m    144\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    145\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_coroutines(\n\u001b[0;32m    146\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mjobs, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpbar, results, max_workers\n\u001b[0;32m    147\u001b[0m     )\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\executor.py:191\u001b[0m, in \u001b[0;36mExecutor._process_coroutines\u001b[1;34m(self, jobs, pbar, results, max_workers)\u001b[0m\n\u001b[0;32m    189\u001b[0m coroutines \u001b[38;5;241m=\u001b[39m [afunc(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs) \u001b[38;5;28;01mfor\u001b[39;00m afunc, args, kwargs, _ \u001b[38;5;129;01min\u001b[39;00m jobs]\n\u001b[0;32m    190\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m future \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m as_completed(coroutines, max_workers):\n\u001b[1;32m--> 191\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m future\n\u001b[0;32m    192\u001b[0m     results\u001b[38;5;241m.\u001b[39mappend(result)\n\u001b[0;32m    193\u001b[0m     pbar\u001b[38;5;241m.\u001b[39mupdate(\u001b[38;5;241m1\u001b[39m)\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\asyncio\\tasks.py:571\u001b[0m, in \u001b[0;36mas_completed.<locals>._wait_for_one\u001b[1;34m()\u001b[0m\n\u001b[0;32m    568\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m f \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m    569\u001b[0m     \u001b[38;5;66;03m# Dummy value from _on_timeout().\u001b[39;00m\n\u001b[0;32m    570\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exceptions\u001b[38;5;241m.\u001b[39mTimeoutError\n\u001b[1;32m--> 571\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mresult\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\asyncio\\futures.py:201\u001b[0m, in \u001b[0;36mFuture.result\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    199\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__log_traceback \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mF<PERSON>e\u001b[39;00m\n\u001b[0;32m    200\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m--> 201\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception\u001b[38;5;241m.\u001b[39mwith_traceback(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception_tb)\n\u001b[0;32m    202\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\asyncio\\tasks.py:232\u001b[0m, in \u001b[0;36mTask.__step\u001b[1;34m(***failed resolving arguments***)\u001b[0m\n\u001b[0;32m    228\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    229\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m exc \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    230\u001b[0m         \u001b[38;5;66;03m# We use the `send` method directly, because coroutines\u001b[39;00m\n\u001b[0;32m    231\u001b[0m         \u001b[38;5;66;03m# don't have `__iter__` and `__next__` methods.\u001b[39;00m\n\u001b[1;32m--> 232\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[43mcoro\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[0;32m    233\u001b[0m     \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[0;32m    234\u001b[0m         result \u001b[38;5;241m=\u001b[39m coro\u001b[38;5;241m.\u001b[39mthrow(exc)\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\executor.py:48\u001b[0m, in \u001b[0;36mas_completed.<locals>.sema_coro\u001b[1;34m(coro)\u001b[0m\n\u001b[0;32m     46\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21msema_coro\u001b[39m(coro):\n\u001b[0;32m     47\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mwith\u001b[39;00m semaphore:\n\u001b[1;32m---> 48\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m coro\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\executor.py:100\u001b[0m, in \u001b[0;36mExecutor.wrap_callable_with_index.<locals>.wrapped_callable_async\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m     98\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m     99\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mraise_exceptions:\n\u001b[1;32m--> 100\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[0;32m    101\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    102\u001b[0m         exec_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtype\u001b[39m(e)\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\executor.py:96\u001b[0m, in \u001b[0;36mExecutor.wrap_callable_with_index.<locals>.wrapped_callable_async\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m     92\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mwrapped_callable_async\u001b[39m(\n\u001b[0;32m     93\u001b[0m     \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs\n\u001b[0;32m     94\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m t\u001b[38;5;241m.\u001b[39mTuple[\u001b[38;5;28mint\u001b[39m, t\u001b[38;5;241m.\u001b[39mCallable \u001b[38;5;241m|\u001b[39m \u001b[38;5;28mfloat\u001b[39m]:\n\u001b[0;32m     95\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 96\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mcallable\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m     97\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m counter, result\n\u001b[0;32m     98\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\testset\\synthesizers\\base.py:94\u001b[0m, in \u001b[0;36mBaseSynthesizer.generate_scenarios\u001b[1;34m(self, n, knowledge_graph, persona_list, callbacks)\u001b[0m\n\u001b[0;32m     88\u001b[0m callbacks \u001b[38;5;241m=\u001b[39m callbacks \u001b[38;5;129;01mor\u001b[39;00m []\n\u001b[0;32m     89\u001b[0m scenario_generation_rm, scenario_generation_group \u001b[38;5;241m=\u001b[39m new_group(\n\u001b[0;32m     90\u001b[0m     name\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname,\n\u001b[0;32m     91\u001b[0m     inputs\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mn\u001b[39m\u001b[38;5;124m\"\u001b[39m: n, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mknowledge_graph\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mstr\u001b[39m(knowledge_graph)},\n\u001b[0;32m     92\u001b[0m     callbacks\u001b[38;5;241m=\u001b[39mcallbacks,\n\u001b[0;32m     93\u001b[0m )\n\u001b[1;32m---> 94\u001b[0m scenarios \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_generate_scenarios(\n\u001b[0;32m     95\u001b[0m     n, knowledge_graph, persona_list, scenario_generation_group\n\u001b[0;32m     96\u001b[0m )\n\u001b[0;32m     97\u001b[0m scenario_generation_rm\u001b[38;5;241m.\u001b[39mon_chain_end(outputs\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mscenarios\u001b[39m\u001b[38;5;124m\"\u001b[39m: scenarios})\n\u001b[0;32m     98\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m scenarios\n", "File \u001b[1;32md:\\miniconda_install\\envs\\pytorch_is\\lib\\site-packages\\ragas\\testset\\synthesizers\\multi_hop\\abstract.py:79\u001b[0m, in \u001b[0;36mMultiHopAbstractQuerySynthesizer._generate_scenarios\u001b[1;34m(self, n, knowledge_graph, persona_list, callbacks)\u001b[0m\n\u001b[0;32m     76\u001b[0m scenarios \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m     78\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(node_clusters) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m---> 79\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m     80\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo clusters found in the knowledge graph. Try changing the relationship condition.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     81\u001b[0m     )\n\u001b[0;32m     82\u001b[0m num_sample_per_cluster \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(np\u001b[38;5;241m.\u001b[39mceil(n \u001b[38;5;241m/\u001b[39m \u001b[38;5;28mlen\u001b[39m(node_clusters)))\n\u001b[0;32m     84\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m cluster \u001b[38;5;129;01min\u001b[39;00m node_clusters:\n", "\u001b[1;31mValueError\u001b[0m: No clusters found in the knowledge graph. Try changing the relationship condition."]}], "source": ["# 尝试使用知识图谱生成测试集\n", "# 这种方法能够生成更复杂的多跳推理问题\n", "try:\n", "    testset = generator.generate(\n", "        testset_size=10,                    # 生成 10 个测试样本\n", "        query_distribution=query_distribution  # 使用配置的查询分布\n", "    )\n", "    \n", "    # 显示生成的测试集\n", "    print(\"基于知识图谱的测试集生成成功！\")\n", "    testset_df = testset.to_pandas()\n", "    print(f\"生成了 {len(testset_df)} 个测试样本\")\n", "    display(testset_df)\n", "    \n", "except ValueError as e:\n", "    print(f\"生成失败：{e}\")\n", "    print(\"\\n可能的原因：\")\n", "    print(\"1. 文档数量较少，无法形成有效的聚类\")\n", "    print(\"2. 文档间相似性不足，缺乏明显的关系\")\n", "    print(\"3. 知识图谱的关系阈值设置过高\")\n", "    print(\"\\n建议解决方案：\")\n", "    print(\"1. 增加更多相关文档\")\n", "    print(\"2. 调整关系条件参数\")\n", "    print(\"3. 使用 generate_with_langchain_docs() 方法作为替代\")"]}, {"cell_type": "markdown", "id": "tutorial_summary", "metadata": {}, "source": ["# 教程总结\n", "\n", "本教程展示了使用 Ragas 库为 RAG 系统生成测试集的完整流程，包括两种主要方法：\n", "\n", "## 🎯 主要收获\n", "\n", "### 1. 基础测试集生成\n", "- **方法**：`generate_with_langchain_docs()`\n", "- **优点**：简单易用，适合快速生成测试集\n", "- **适用场景**：文档数量较少，需要快速验证 RAG 系统基本功能\n", "\n", "### 2. 基于知识图谱的高级生成\n", "- **方法**：构建知识图谱 + `generate()`\n", "- **优点**：能生成更复杂的多跳推理问题，测试质量更高\n", "- **挑战**：需要足够的文档数量和文档间关系\n", "\n", "## 🔧 技术要点\n", "\n", "### 模型配置\n", "- **LLM**：用于生成问题和答案（本例使用通义千问）\n", "- **嵌入模型**：用于文档向量化和相似性计算（本例使用 Jina AI）\n", "\n", "### 知识图谱转换\n", "- **标题提取**：识别文档结构\n", "- **摘要生成**：提取核心信息\n", "- **实体识别**：提取关键实体\n", "- **关系建模**：建立文档间联系\n", "\n", "### 查询类型\n", "- **单跳查询**：直接从单个文档回答\n", "- **多跳查询**：需要跨文档推理\n", "- **特定查询**：针对特定主题或实体\n", "\n", "## 💡 最佳实践\n", "\n", "1. **文档准备**\n", "   - 确保文档质量和相关性\n", "   - 保持适当的文档数量（建议 10+ 个相关文档）\n", "   - 文档间应有一定的主题关联性\n", "\n", "2. **模型选择**\n", "   - LLM 选择：优先考虑理解能力强的模型\n", "   - 嵌入模型：选择支持中文且效果好的模型\n", "   - 保持模型配置的一致性\n", "\n", "3. **错误处理**\n", "   - 知识图谱生成可能失败，需要有备选方案\n", "   - 监控生成过程，及时发现问题\n", "   - 验证生成的测试集质量\n", "\n", "## 🚀 后续步骤\n", "\n", "1. **测试集评估**：使用生成的测试集评估 RAG 系统性能\n", "2. **迭代优化**：根据评估结果调整文档和生成策略\n", "3. **扩展应用**：将方法应用到更大规模的文档集合\n", "4. **自定义优化**：根据具体业务需求调整生成参数\n", "\n", "## 📚 相关资源\n", "\n", "- [Ragas 官方文档](https://docs.ragas.org.cn/)\n", "- [<PERSON><PERSON><PERSON><PERSON> 文档加载器](https://python.langchain.com/docs/modules/data_connection/document_loaders/)\n", "- [RAG 系统评估指南](https://docs.ragas.org.cn/en/stable/concepts/metrics/)\n", "\n", "---\n", "\n", "**注意**：本教程使用的 API 密钥仅为示例，实际使用时请替换为您自己的密钥。"]}], "metadata": {"kernelspec": {"display_name": "pytorch_is", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}