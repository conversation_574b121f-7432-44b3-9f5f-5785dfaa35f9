#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Jina AI 嵌入模块
"""

import lazyllm
from lazyllm.module import OnlineEmbeddingModuleBase
from typing import Dict, List, Union
import json

# 自定义 Jina AI 嵌入模块
class JinaEmbeddingModule(OnlineEmbeddingModuleBase):
    """Jina AI 嵌入模块 - 符合 OpenAI 格式"""
    
    def __init__(self, api_key: str, model_name: str = "jina-embeddings-v3", task: str = "text-matching"):
        # Jina AI 的 API 端点
        embed_url = "https://api.jina.ai/v1/embeddings"
        
        super().__init__(
            model_series="openai",  # 使用 openai 格式
            embed_url=embed_url,
            api_key=api_key,
            embed_model_name=model_name
        )
        self.task = task
    
    def _encapsulated_data(self, text: str, **kwargs) -> Dict:
        """封装请求数据为 OpenAI/Jina AI 格式"""
        # 如果是单个文本，转换为列表
        if isinstance(text, str):
            input_texts = [text]
        else:
            input_texts = text if isinstance(text, list) else [text]
            
        json_data = {
            "model": self._embed_model_name,
            "input": input_texts,
            "task": self.task  # Jina AI 特有的参数
        }
        
        # 添加其他参数
        if len(kwargs) > 0:
            json_data.update(kwargs)
            
        return json_data
    
    def _parse_response(self, response: Union[List, Dict]) -> Union[List[List[float]], List[float]]:
        """解析 OpenAI/Jina AI 格式的响应"""
        if isinstance(response, dict) and "data" in response:
            # OpenAI/Jina AI 返回格式: {"data": [{"embedding": [...]}, ...]}
            embeddings = [item["embedding"] for item in response["data"]]
            return embeddings[0] if len(embeddings) == 1 else embeddings
        else:
            raise ValueError(f"无法解析响应格式: {response}")

def test_jina_embedding():
    """测试 Jina AI 嵌入模块"""
    print("🚀 开始测试 Jina AI 嵌入模块")
    print("=" * 50)
    
    # 创建 Jina AI 嵌入模块实例
    jina_embedding = JinaEmbeddingModule(
        api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
        model_name="jina-embeddings-v3",
        task="text-matching"
    )
    
    # 测试文本（对应您 curl 命令中的文本）
    test_texts = [
        "Organic skincare for sensitive skin with aloe vera and chamomile: Imagine the soothing embrace of nature with our organic skincare range, crafted specifically for sensitive skin. Infused with the calming properties of aloe vera and chamomile, each product provides gentle nourishment and protection. Say goodbye to irritation and hello to a glowing, healthy complexion.",
        "Bio-Hautpflege für empfindliche Haut mit Aloe Vera und Kamille: Erleben Sie die wohltuende Wirkung unserer Bio-Hautpflege, speziell für empfindliche Haut entwickelt. Mit den beruhigenden Eigenschaften von Aloe Vera und Kamille pflegen und schützen unsere Produkte Ihre Haut auf natürliche Weise. Verabschieden Sie sich von Hautirritationen und genießen Sie einen strahlenden Teint.",
        "Cuidado de la piel orgánico para piel sensible con aloe vera y manzanilla: Descubre el poder de la naturaleza con nuestra línea de cuidado de la piel orgánico, diseñada especialmente para pieles sensibles. Enriquecidos con aloe vera y manzanilla, estos productos ofrecen una hidratación y protección suave. Despídete de las irritaciones y saluda a una piel radiante y saludable.",
        "针对敏感肌专门设计的天然有机护肤产品：体验由芦荟和洋甘菊提取物带来的自然呵护。我们的护肤产品特别为敏感肌设计，温和滋润，保护您的肌肤不受刺激。让您的肌肤告别不适，迎来健康光彩。",
        "新しいメイクのトレンドは鮮やかな色と革新的な技術に焦点を当てています: 今シーズンのメイクアップトレンドは、大胆な色彩と革新的な技術に注目しています。ネオンアイライナーからホログラフィックハイライターまで、クリエイティビティを解き放ち、毎回ユニークなルックを演出しましょう。"
    ]
    
    print("📝 测试文本:")
    for i, text in enumerate(test_texts, 1):
        print(f"{i}. {text[:50]}...")
    
    print("\n🔧 测试单个文本嵌入...")
    try:
        # 测试单个文本
        single_embedding = jina_embedding(test_texts[0])
        print(f"✅ 单个文本嵌入成功!")
        print(f"   嵌入维度: {len(single_embedding)}")
        print(f"   前5个值: {single_embedding[:5]}")
        print(f"   数据类型: {type(single_embedding)}")
        
    except Exception as e:
        print(f"❌ 单个文本嵌入失败: {e}")
        return False
    
    print("\n🔧 测试批量文本嵌入...")
    try:
        # 测试批量文本（如果支持）
        # 注意：可能需要逐个处理，取决于 LazyLLM 的实现
        batch_embeddings = []
        for text in test_texts:
            embedding = jina_embedding(text)
            batch_embeddings.append(embedding)
        
        print(f"✅ 批量文本嵌入成功!")
        print(f"   处理文本数量: {len(batch_embeddings)}")
        print(f"   每个嵌入维度: {len(batch_embeddings[0])}")
        
        # 计算相似度示例
        if len(batch_embeddings) >= 2:
            import numpy as np
            vec1 = np.array(batch_embeddings[0])
            vec2 = np.array(batch_embeddings[3])  # 中文文本
            similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
            print(f"   英文与中文文本相似度: {similarity:.4f}")
        
    except Exception as e:
        print(f"❌ 批量文本嵌入失败: {e}")
        return False
    
    print("\n🎉 所有测试通过!")
    return True

def test_with_document():
    """测试与 LazyLLM Document 的集成"""
    print("\n📚 测试与 LazyLLM Document 的集成...")
    
    try:
        # 创建 Jina AI 嵌入模块实例
        jina_embedding = JinaEmbeddingModule(
            api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
            model_name="jina-embeddings-v3",
            task="text-matching"
        )
        
        print("✅ Jina AI 嵌入模块创建成功")
        print("📋 配置信息:")
        print(f"   模型: {jina_embedding._embed_model_name}")
        print(f"   任务类型: {jina_embedding.task}")
        print(f"   API 端点: {jina_embedding._embed_url}")
        
        # 注意：实际的 Document 测试需要真实的文档路径
        # documents = lazyllm.Document(dataset_path="/path/to/your/doc/dir",
        #                              embed=jina_embedding,
        #                              manager=False)
        
        print("✅ 集成测试准备完成")
        print("💡 提示: 请将 '/path/to/your/doc/dir' 替换为实际的文档路径")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 Jina AI 嵌入模块测试")
    print("=" * 60)
    
    # 基本功能测试
    success = test_jina_embedding()
    
    if success:
        # 集成测试
        test_with_document()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成! Jina AI 嵌入模块可以正常使用")
    else:
        print("❌ 测试失败! 请检查配置和网络连接")
