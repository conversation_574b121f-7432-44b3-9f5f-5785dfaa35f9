---
title: "Being Inclusive"
description: "We are all responsible for creating and maintaining an inclusive environment at GitLab."
---

## What is fundamental for a successful inclusive environment

We are **all responsible** for creating and maintaining an inclusive environment at GitLab.

We all need to feel **empowered** to contribute and collaborate.

## Diversity, Inclusion & Belonging at GitLab

Diversity includes all the differences we each have, whether it be where we grew up, where we went to school, experiences, how we react, age, race, gender, national origin, things we can and can not see, the list goes on.

Inclusion is understanding or recognizing all these differences and inviting someone to be a part of things and collaborate, etc.

Belonging is when you feel your insights and contributions are valued. It goes back to team members feeling they can bring their full selves to work.

See the [Diversity, Inclusion, and Belonging page]({{< ref "inclusion" >}}) page for more information.

## What inclusive behaviors look like

1. Include and seek input from team members across a wide variety of backgrounds.
1. Active listening - listen carefully to the person speaking and playback what they have said in an effort to show understanding.
1. Make a habit of asking questions.
1. If you have a strong reaction to someone, ask yourself why. Look inward.
1. Address misunderstandings and quickly resolve disagreements.
1. Make a point to understand each team member's contribution efforts and leverage them as much as possible.
1. Ensure all voices are heard. Include everyone as much as possible in discussions.
1. Assume positive intent and examine your assumptions/judgements.

See our values page for futher information on [inclusive language]({{< ref "values#inclusive-language--pronouns" >}}).

## Inclusion Training

In December 2019, we held 3 sessions on Inclusion Training. Below you can find a recorded session that follows along with the [slide deck](https://docs.google.com/presentation/d/1WujXXxNDorIXB3NJeEnneC0dnOoqdFtcL6OhM-zWt4s/edit?usp=sharing) and [agenda](https://docs.google.com/document/d/1za96EEONFnOp-cI1kflIstE-MIAKSlsYZbivjnhr6ys/edit?usp=sharing).

{{< youtube "gsQ2OsmgqVM" >}}

## Inclusion Knowledge Assessment

Anyone can earn a GitLab Inclusion Certification. To obtain certification, you will need to complete the **[Inclusion Knowledge Assessment Quiz](https://docs.google.com/forms/d/e/1FAIpQLSet0MW_GSOJUQkD3EMBLrSm3POKU6Y4opk_zFq31rFLCcWzwQ/viewform)** and earn at least an 80%. Once the quiz has been passed, you will receive an email with your certification that you can share on your personal LinkedIn or Twitter pages. If you have questions, please reach out to our L&D team at `<EMAIL>`.
