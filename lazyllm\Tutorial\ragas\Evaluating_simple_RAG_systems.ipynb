{"cells": [{"cell_type": "code", "execution_count": 1, "id": "61bce990", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型配置完成 / Model configuration completed\n", "LLM模型: qwen-plus-2025-01-25\n", "嵌入模型: <PERSON><PERSON> Embeddings\n"]}], "source": ["# ========================================\n", "# RAG系统核心组件配置\n", "# RAG System Core Components Configuration\n", "# ========================================\n", "\n", "# 导入必要的库和模块\n", "# Import necessary libraries and modules\n", "from langchain_openai import ChatOpenAI          # LangChain的OpenAI兼容接口\n", "from jina_embeddings import create_jina_embeddings  # Jina嵌入模型创建函数\n", "import numpy as np                               # 数值计算库，用于向量操作\n", "\n", "print(\"🚀 开始配置RAG系统核心组件...\")\n", "print(\"🚀 Starting RAG system core components configuration...\")\n", "\n", "# ========================================\n", "# 1. 配置大语言模型 (LLM)\n", "# 1. Configure Large Language Model (LLM)\n", "# ========================================\n", "\n", "print(\"\\n🤖 配置大语言模型...\")\n", "llm = ChatOpenAI(\n", "    # 阿里云DashScope API端点，兼容OpenAI格式\n", "    openai_api_base=\"https://dashscope.aliyuncs.com/compatible-mode/v1\", \n", "    \n", "    # API密钥 (实际使用时请替换为您自己的密钥)\n", "    openai_api_key=\"sk-282f3112bd714d6e85540da173b5517c\", \n", "    \n", "    # 模型名称：通义千问Plus 2025年1月25日版本\n", "    # 这是阿里云的高性能大语言模型，支持中英文对话\n", "    model=\"qwen-plus-2025-01-25\"\n", ")\n", "\n", "print(f\"✅ LLM配置完成: {llm.model_name}\")\n", "print(f\"   - API端点: DashScope兼容模式\")\n", "print(f\"   - 模型特点: 支持中英文，推理能力强\")\n", "\n", "# ========================================\n", "# 2. 配置嵌入模型 (Embeddings)\n", "# 2. Configure Embedding Model\n", "# ========================================\n", "\n", "print(\"\\n🔢 配置嵌入模型...\")\n", "embeddings = create_jina_embeddings(\n", "    # Jina AI的API密钥 (实际使用时请替换为您自己的密钥)\n", "    # Jina Embeddings是专门为检索任务优化的嵌入模型\n", "    api_key='jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR'\n", ")\n", "\n", "print(f\"✅ 嵌入模型配置完成: Jina Embeddings\")\n", "print(f\"   - 模型特点: 专为检索优化，支持多语言\")\n", "print(f\"   - 向量维度: 通常为1024维\")\n", "print(f\"   - 适用场景: 文档检索、语义搜索\")\n", "\n", "# ========================================\n", "# 3. 配置完成确认\n", "# 3. Configuration Completion Confirmation\n", "# ========================================\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"🎉 RAG系统核心组件配置完成!\")\n", "print(\"🎉 RAG system core components configuration completed!\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"\\n📋 配置摘要 / Configuration Summary:\")\n", "print(f\"   🤖 语言模型: {llm.model_name}\")\n", "print(f\"   🔢 嵌入模型: Jina Embeddings\")\n", "print(f\"   🔧 数值计算: NumPy {np.__version__}\")\n", "print(f\"   ✅ 状态: 就绪 (Ready)\")\n", "\n", "print(f\"\\n💡 提示: 现在可以开始构建RAG类和加载文档了\")\n", "print(f\"💡 Tip: Now ready to build RAG class and load documents\")"]}, {"cell_type": "markdown", "id": "5cc758d4", "metadata": {}, "source": ["# 构建一个简单的RAG系统\n", "\n", "本notebook演示如何构建和评估一个简单的检索增强生成(RAG)系统。\n", "\n", "## 主要功能：\n", "- 使用阿里云通义千问模型作为语言模型\n", "- 使用Jina Embeddings进行文档嵌入\n", "- 实现文档检索和答案生成\n", "- 支持相似度计算和最相关文档检索\n"]}, {"cell_type": "code", "execution_count": 2, "id": "90b96e31", "metadata": {}, "outputs": [], "source": ["class RAG:\n", "    \"\"\"\n", "    简单的检索增强生成(RAG)系统类\n", "    Simple Retrieval-Augmented Generation (RAG) system class\n", "    \n", "    该类实现了基本的RAG功能：\n", "    - 文档加载和嵌入计算\n", "    - 基于查询的相关文档检索\n", "    - 基于检索文档的答案生成\n", "    \"\"\"\n", "    \n", "    def __init__(self, llm_model=None, embedding_model=None):\n", "        \"\"\"\n", "        初始化RAG系统\n", "        Initialize RAG system\n", "        \n", "        Args:\n", "            llm_model: 语言模型实例，如果为None则使用全局配置的llm\n", "            embedding_model: 嵌入模型实例，如果为None则使用全局配置的embeddings\n", "        \"\"\"\n", "        # 使用传入的模型或全局配置的模型\n", "        # Use provided models or globally configured models\n", "        self.llm = llm_model if llm_model is not None else llm\n", "        self.embeddings = embedding_model if embedding_model is not None else embeddings\n", "        \n", "        # 初始化文档存储\n", "        # Initialize document storage\n", "        self.doc_embeddings = None  # 文档嵌入向量列表\n", "        self.docs = None           # 原始文档列表\n", "        \n", "        print(\"RAG系统初始化完成 / RAG system initialized\")\n", "\n", "    def load_documents(self, documents):\n", "        \"\"\"\n", "        加载文档并计算其嵌入向量\n", "        Load documents and compute their embeddings\n", "        \n", "        Args:\n", "            documents (list): 文档字符串列表\n", "        \"\"\"\n", "        print(f\"正在加载 {len(documents)} 个文档... / Loading {len(documents)} documents...\")\n", "        \n", "        # 存储原始文档\n", "        # Store original documents\n", "        self.docs = documents\n", "        \n", "        # 计算文档嵌入向量\n", "        # Compute document embeddings\n", "        self.doc_embeddings = self.embeddings.embed_documents(documents)\n", "        \n", "        print(f\"文档加载完成，共计算 {len(self.doc_embeddings)} 个嵌入向量\")\n", "        print(f\"Document loading completed, computed {len(self.doc_embeddings)} embeddings\")\n", "\n", "    def get_most_relevant_docs(self, query, top_k=1):\n", "        \"\"\"\n", "        根据查询找到最相关的文档\n", "        Find the most relevant documents for a given query\n", "        \n", "        Args:\n", "            query (str): 用户查询\n", "            top_k (int): 返回最相关的文档数量，默认为1\n", "            \n", "        Returns:\n", "            list: 最相关的文档列表\n", "        \"\"\"\n", "        # 检查文档是否已加载\n", "        # Check if documents are loaded\n", "        if not self.docs or not self.doc_embeddings:\n", "            raise ValueError(\"文档和嵌入向量未加载。请先调用load_documents()方法。\")\n", "\n", "        print(f\"正在检索与查询相关的文档: '{query}'\")\n", "        print(f\"Retrieving documents relevant to query: '{query}'\")\n", "        \n", "        # 计算查询的嵌入向量\n", "        # Compute query embedding\n", "        query_embedding = self.embeddings.embed_query(query)\n", "        \n", "        # 计算余弦相似度\n", "        # Calculate cosine similarity\n", "        similarities = [\n", "            np.dot(query_embedding, doc_emb) / \n", "            (np.linalg.norm(query_embedding) * np.linalg.norm(doc_emb))\n", "            for doc_emb in self.doc_embeddings\n", "        ]\n", "        \n", "        # 获取最相关的文档索引\n", "        # Get indices of most relevant documents\n", "        top_indices = np.argsort(similarities)[-top_k:][::-1]\n", "        \n", "        # 返回最相关的文档\n", "        # Return most relevant documents\n", "        relevant_docs = [self.docs[i] for i in top_indices]\n", "        \n", "        print(f\"找到 {len(relevant_docs)} 个相关文档\")\n", "        print(f\"Found {len(relevant_docs)} relevant documents\")\n", "        \n", "        return relevant_docs\n", "\n", "    def generate_answer(self, query, relevant_docs):\n", "        \"\"\"\n", "        基于最相关的文档生成答案\n", "        Generate an answer based on the most relevant documents\n", "        \n", "        Args:\n", "            query (str): 用户查询\n", "            relevant_docs (list): 相关文档列表\n", "            \n", "        Returns:\n", "            str: 生成的答案\n", "        \"\"\"\n", "        print(\"正在生成答案... / Generating answer...\")\n", "        \n", "        # 构建提示词\n", "        # Build prompt\n", "        docs_text = \"\\n\\n\".join([f\"文档{i+1}: {doc}\" for i, doc in enumerate(relevant_docs)])\n", "        prompt = f\"问题: {query}\\n\\n相关文档:\\n{docs_text}\"\n", "        \n", "        # 构建消息\n", "        # Build messages\n", "        messages = [\n", "            (\"system\", \"你是一个有用的助手，只基于给定的文档回答问题。请用中文回答，如果文档中没有相关信息，请明确说明。\"),\n", "            (\"human\", prompt),\n", "        ]\n", "        \n", "        # 调用语言模型生成答案\n", "        # Invoke language model to generate answer\n", "        ai_msg = self.llm.invoke(messages)\n", "        \n", "        print(\"答案生成完成 / Answer generation completed\")\n", "        return ai_msg.content\n", "    \n", "    def query(self, question, top_k=1):\n", "        \"\"\"\n", "        完整的RAG查询流程：检索 + 生成\n", "        Complete RAG query process: retrieval + generation\n", "        \n", "        Args:\n", "            question (str): 用户问题\n", "            top_k (int): 检索的文档数量\n", "            \n", "        Returns:\n", "            dict: 包含问题、相关文档和答案的字典\n", "        \"\"\"\n", "        # 检索相关文档\n", "        # Retrieve relevant documents\n", "        relevant_docs = self.get_most_relevant_docs(question, top_k)\n", "        \n", "        # 生成答案\n", "        # Generate answer\n", "        answer = self.generate_answer(question, relevant_docs)\n", "        \n", "        return {\n", "            \"question\": question,\n", "            \"relevant_documents\": relevant_docs,\n", "            \"answer\": answer\n", "        }"]}, {"cell_type": "code", "execution_count": 3, "id": "4437ab35", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准备了 7 个示例文档\n", "Prepared 7 sample documents\n", "\n", "文档内容预览 / Document content preview:\n", "1. 阿尔伯特·爱因斯坦提出了相对论，这一理论彻底改变了我们对时间、空间和重力的理解。相对论包括狭义相对论...\n", "2. 玛丽·居里是一位物理学家和化学家，她在放射性研究方面做出了开创性贡献，并获得了两次诺贝尔奖。她是第一...\n", "3. 艾萨克·牛顿制定了运动定律和万有引力定律，为经典力学奠定了基础。牛顿的三大运动定律至今仍是物理学的基...\n", "4. 查尔斯·达尔文在其著作《物种起源》中提出了自然选择的进化论。这一理论解释了生物物种的多样性和进化过程...\n", "5. 阿达·洛夫莱斯被认为是第一位计算机程序员，她为查尔斯·巴贝奇的早期机械计算机分析机编写了算法。她预见...\n", "6. 尼古拉·特斯拉是一位杰出的发明家和电气工程师，他在交流电系统、无线技术和电磁学方面做出了重要贡献。...\n", "7. 斯蒂芬·霍金是著名的理论物理学家，他在黑洞理论和宇宙学方面做出了突破性贡献，著有《时间简史》等科普著...\n"]}], "source": ["# 准备示例文档数据\n", "# Prepare sample document data\n", "# 这些文档包含了著名科学家的基本信息，用于演示RAG系统的检索和生成能力\n", "\n", "sample_docs = [\n", "    # 物理学家 - 爱因斯坦：相对论的提出者\n", "    \"阿尔伯特·爱因斯坦提出了相对论，这一理论彻底改变了我们对时间、空间和重力的理解。相对论包括狭义相对论和广义相对论两部分。\",\n", "    \n", "    # 物理学家/化学家 - 居里夫人：放射性研究先驱\n", "    \"玛丽·居里是一位物理学家和化学家，她在放射性研究方面做出了开创性贡献，并获得了两次诺贝尔奖。她是第一位获得诺贝尔奖的女性。\",\n", "    \n", "    # 物理学家 - 牛顿：经典力学奠基人\n", "    \"艾萨克·牛顿制定了运动定律和万有引力定律，为经典力学奠定了基础。牛顿的三大运动定律至今仍是物理学的基石。\",\n", "    \n", "    # 生物学家 - 达尔文：进化论创立者\n", "    \"查尔斯·达尔文在其著作《物种起源》中提出了自然选择的进化论。这一理论解释了生物物种的多样性和进化过程。\",\n", "    \n", "    # 计算机科学先驱 - 洛夫莱斯：第一位程序员\n", "    \"阿达·洛夫莱斯被认为是第一位计算机程序员，她为查尔斯·巴贝奇的早期机械计算机分析机编写了算法。她预见了计算机的巨大潜力。\",\n", "    \n", "    # 发明家/工程师 - 特斯拉：电气工程先驱\n", "    \"尼古拉·特斯拉是一位杰出的发明家和电气工程师，他在交流电系统、无线技术和电磁学方面做出了重要贡献。\",\n", "    \n", "    # 理论物理学家 - 霍金：现代宇宙学大师\n", "    \"斯蒂芬·霍金是著名的理论物理学家，他在黑洞理论和宇宙学方面做出了突破性贡献，著有《时间简史》等科普著作。\"\n", "]\n", "\n", "# 显示文档统计信息\n", "# Display document statistics\n", "print(f\"📚 准备了 {len(sample_docs)} 个示例文档\")\n", "print(f\"📚 Prepared {len(sample_docs)} sample documents\")\n", "\n", "# 计算文档的基本统计信息\n", "# Calculate basic document statistics\n", "total_chars = sum(len(doc) for doc in sample_docs)\n", "avg_chars = total_chars / len(sample_docs)\n", "print(f\"📊 文档统计: 总字符数 {total_chars}, 平均长度 {avg_chars:.1f} 字符\")\n", "print(f\"📊 Document stats: Total {total_chars} chars, Average {avg_chars:.1f} chars\")\n", "\n", "# 显示文档内容预览\n", "# Display document content preview\n", "print(\"\\n📖 文档内容预览 / Document content preview:\")\n", "for i, doc in enumerate(sample_docs, 1):\n", "    # 显示每个文档的前50个字符作为预览\n", "    preview = doc[:50] + \"...\" if len(doc) > 50 else doc\n", "    print(f\"  {i}. {preview}\")\n", "\n", "print(f\"\\n✅ 文档数据准备完成，可以开始构建RAG系统\")\n", "print(f\"✅ Document data preparation completed, ready to build RAG system\")"]}, {"cell_type": "code", "execution_count": 4, "id": "62f259ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "初始化RAG系统 / Initializing RAG System\n", "==================================================\n", "RAG系统初始化完成 / RAG system initialized\n", "\n", "==================================================\n", "加载文档 / Loading Documents\n", "==================================================\n", "正在加载 7 个文档... / Loading 7 documents...\n", "文档加载完成，共计算 7 个嵌入向量\n", "Document loading completed, computed 7 embeddings\n", "\n", "==================================================\n", "测试查询1 / Test Query 1\n", "==================================================\n", "正在检索与查询相关的文档: '谁提出了相对论？'\n", "Retrieving documents relevant to query: '谁提出了相对论？'\n", "找到 1 个相关文档\n", "Found 1 relevant documents\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n", "\n", "问题: 谁提出了相对论？\n", "相关文档: 阿尔伯特·爱因斯坦提出了相对论，这一理论彻底改变了我们对时间、空间和重力的理解。相对论包括狭义相对论和广义相对论两部分。...\n", "答案: 阿尔伯特·爱因斯坦提出了相对论。\n", "\n", "==================================================\n", "测试查询2 / Test Query 2\n", "==================================================\n", "正在检索与查询相关的文档: '谁是第一位计算机程序员？'\n", "Retrieving documents relevant to query: '谁是第一位计算机程序员？'\n", "找到 1 个相关文档\n", "Found 1 relevant documents\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n", "\n", "问题: 谁是第一位计算机程序员？\n", "相关文档: 阿达·洛夫莱斯被认为是第一位计算机程序员，她为查尔斯·巴贝奇的早期机械计算机分析机编写了算法。她预见了计算机的巨大潜力。...\n", "答案: 根据提供的文档，阿达·洛夫莱斯被认为是第一位计算机程序员。她为查尔斯·巴贝奇的早期机械计算机分析机编写了算法，并且预见了计算机的巨大潜力。\n", "\n", "==================================================\n", "测试查询3 / Test Query 3\n", "==================================================\n", "正在检索与查询相关的文档: '达尔文的主要贡献是什么？'\n", "Retrieving documents relevant to query: '达尔文的主要贡献是什么？'\n", "找到 1 个相关文档\n", "Found 1 relevant documents\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n", "\n", "问题: 达尔文的主要贡献是什么？\n", "相关文档: 查尔斯·达尔文在其著作《物种起源》中提出了自然选择的进化论。这一理论解释了生物物种的多样性和进化过程。...\n", "答案: 达尔文的主要贡献是提出了自然选择的进化论，这一理论在他著作的《物种起源》中被详细阐述，解释了生物物种的多样性和进化过程。\n", "\n", "==================================================\n", "RAG系统测试完成 / RAG System Testing Completed\n", "==================================================\n"]}], "source": ["# ========================================\n", "# RAG系统基础功能测试\n", "# RAG System Basic Functionality Testing\n", "# ========================================\n", "\n", "# 第一步：初始化RAG系统实例\n", "# Step 1: Initialize RAG system instance\n", "print(\"=\" * 60)\n", "print(\"🔧 初始化RAG系统 / Initializing RAG System\")\n", "print(\"=\" * 60)\n", "\n", "# 创建RAG实例，使用之前配置的LLM和嵌入模型\n", "# Create RAG instance using previously configured LLM and embedding models\n", "rag = RAG()\n", "print(\"✅ RAG系统实例创建成功\")\n", "\n", "# 第二步：加载文档到RAG系统\n", "# Step 2: Load documents into RAG system\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"📚 加载文档到RAG系统 / Loading Documents into RAG System\")\n", "print(\"=\" * 60)\n", "\n", "# 将准备好的示例文档加载到RAG系统中\n", "# 这个过程包括：文档存储 + 嵌入向量计算\n", "rag.load_documents(sample_docs)\n", "print(\"✅ 文档加载和向量化完成\")\n", "\n", "# ========================================\n", "# 开始进行多个测试查询\n", "# Start multiple test queries\n", "# ========================================\n", "\n", "# 测试查询1：物理学问题 - 相对论\n", "# Test query 1: Physics question - Theory of relativity\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🧪 测试查询1: 物理学问题 / Test Query 1: Physics Question\")\n", "print(\"=\" * 60)\n", "\n", "query1 = \"谁提出了相对论？\"\n", "print(f\"🔍 查询内容: {query1}\")\n", "print(f\"🎯 预期答案: 应该检索到爱因斯坦相关文档\")\n", "\n", "# 执行完整的RAG查询流程：检索 + 生成\n", "result1 = rag.query(query1)\n", "\n", "print(f\"\\n📝 查询结果:\")\n", "print(f\"   问题: {result1['question']}\")\n", "print(f\"   相关文档: {result1['relevant_documents'][0][:100]}...\")\n", "print(f\"   生成答案: {result1['answer']}\")\n", "\n", "# 测试查询2：计算机科学问题 - 编程历史\n", "# Test query 2: Computer science question - Programming history\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🧪 测试查询2: 计算机科学问题 / Test Query 2: Computer Science Question\")\n", "print(\"=\" * 60)\n", "\n", "query2 = \"谁是第一位计算机程序员？\"\n", "print(f\"🔍 查询内容: {query2}\")\n", "print(f\"🎯 预期答案: 应该检索到阿达·洛夫莱斯相关文档\")\n", "\n", "# 执行RAG查询\n", "result2 = rag.query(query2)\n", "\n", "print(f\"\\n📝 查询结果:\")\n", "print(f\"   问题: {result2['question']}\")\n", "print(f\"   相关文档: {result2['relevant_documents'][0][:100]}...\")\n", "print(f\"   生成答案: {result2['answer']}\")\n", "\n", "# 测试查询3：生物学问题 - 进化论\n", "# Test query 3: Biology question - Evolution theory\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🧪 测试查询3: 生物学问题 / Test Query 3: Biology Question\")\n", "print(\"=\" * 60)\n", "\n", "query3 = \"达尔文的主要贡献是什么？\"\n", "print(f\"🔍 查询内容: {query3}\")\n", "print(f\"🎯 预期答案: 应该检索到达尔文进化论相关文档\")\n", "\n", "# 执行RAG查询\n", "result3 = rag.query(query3)\n", "\n", "print(f\"\\n📝 查询结果:\")\n", "print(f\"   问题: {result3['question']}\")\n", "print(f\"   相关文档: {result3['relevant_documents'][0][:100]}...\")\n", "print(f\"   生成答案: {result3['answer']}\")\n", "\n", "# ========================================\n", "# 基础测试完成总结\n", "# Basic testing completion summary\n", "# ========================================\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🎉 RAG系统基础功能测试完成 / RAG System Basic Testing Completed\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n📊 测试总结:\")\n", "print(f\"   ✅ 测试查询数量: 3个\")\n", "print(f\"   ✅ 涵盖领域: 物理学、计算机科学、生物学\")\n", "print(f\"   ✅ 检索功能: 正常\")\n", "print(f\"   ✅ 生成功能: 正常\")\n", "print(f\"   ✅ 中文支持: 正常\")\n", "\n", "print(f\"\\n🚀 准备进行高级功能测试...\")\n", "print(f\"🚀 Ready for advanced functionality testing...\")"]}, {"cell_type": "code", "execution_count": 5, "id": "evaluation_demo", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "RAG系统高级功能演示 / Advanced RAG System Features Demo\n", "============================================================\n", "\n", "1. 多文档检索测试 / Multi-document Retrieval Test\n", "----------------------------------------\n", "正在检索与查询相关的文档: '物理学家有哪些重要贡献？'\n", "Retrieving documents relevant to query: '物理学家有哪些重要贡献？'\n", "找到 3 个相关文档\n", "Found 3 relevant documents\n", "查询: 物理学家有哪些重要贡献？\n", "检索到 3 个相关文档:\n", "  1. 阿尔伯特·爱因斯坦提出了相对论，这一理论彻底改变了我们对时间、空间和重力的理解。相对论包括狭义相对论和广义相对论两部分。...\n", "  2. 艾萨克·牛顿制定了运动定律和万有引力定律，为经典力学奠定了基础。牛顿的三大运动定律至今仍是物理学的基石。...\n", "  3. 斯蒂芬·霍金是著名的理论物理学家，他在黑洞理论和宇宙学方面做出了突破性贡献，著有《时间简史》等科普著作。...\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n", "\n", "综合答案: 根据提供的文档，以下是一些物理学家的重要贡献：\n", "\n", "1. **阿尔伯特·爱因斯坦**：提出了相对论，包括狭义相对论和广义相对论，这一理论彻底改变了我们对时间、空间和重力的理解。\n", "   \n", "2. **艾萨克·牛顿**：制定了运动定律和万有引力定律，为经典力学奠定了基础。他的三大运动定律至今仍是物理学的基石。\n", "\n", "3. **斯蒂芬·霍金**：在黑洞理论和宇宙学方面做出了突破性贡献，并通过著作如《时间简史》向公众普及科学知识。\n", "\n", "\n", "2. 不相关查询测试 / Irrelevant Query Test\n", "----------------------------------------\n", "正在检索与查询相关的文档: '今天天气怎么样？'\n", "Retrieving documents relevant to query: '今天天气怎么样？'\n", "找到 1 个相关文档\n", "Found 1 relevant documents\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n", "查询: 今天天气怎么样？\n", "最相关文档: 阿尔伯特·爱因斯坦提出了相对论，这一理论彻底改变了我们对时间、空间和重力的理解。相对论包括狭义相对论和广义相对论两部分。...\n", "答案: 文档中没有提供关于今天天气的信息。\n", "\n", "\n", "3. 系统配置信息 / System Configuration Info\n", "----------------------------------------\n", "语言模型: 阿里云通义千问 (qwen-plus-2025-01-25)\n", "嵌入模型: <PERSON><PERSON> Embeddings\n", "文档数量: 7\n", "嵌入维度: 1024\n", "检索方法: 余弦相似度\n", "支持功能: 多文档检索、中文问答、相似度计算\n", "\n", "============================================================\n", "演示完成！RAG系统已成功配置并可以使用\n", "Demo completed! RAG system is successfully configured and ready to use\n", "============================================================\n"]}], "source": ["# ========================================\n", "# RAG系统高级功能演示和性能测试\n", "# RAG System Advanced Features Demo and Performance Testing\n", "# ========================================\n", "\n", "print(\"=\" * 70)\n", "print(\"🚀 RAG系统高级功能演示 / Advanced RAG System Features Demo\")\n", "print(\"=\" * 70)\n", "\n", "# ========================================\n", "# 测试1: 多文档检索功能\n", "# Test 1: Multi-document Retrieval Feature\n", "# ========================================\n", "\n", "print(\"\\n🔍 测试1: 多文档检索功能 / Multi-document Retrieval Test\")\n", "print(\"-\" * 50)\n", "print(\"📝 目标: 测试系统能否检索多个相关文档并综合生成答案\")\n", "print(\"📝 Goal: Test if system can retrieve multiple relevant docs and synthesize answer\")\n", "\n", "# 设计一个需要多个文档信息的查询\n", "# Design a query that requires information from multiple documents\n", "query_multi = \"物理学家有哪些重要贡献？\"\n", "print(f\"\\n🔍 查询: {query_multi}\")\n", "print(f\"🎯 预期: 应该检索到爱因斯坦、牛顿、霍金等多位物理学家的文档\")\n", "\n", "# 检索前3个最相关的文档\n", "# Retrieve top 3 most relevant documents\n", "relevant_docs_multi = rag.get_most_relevant_docs(query_multi, top_k=3)\n", "\n", "print(f\"\\n📚 检索结果: 找到 {len(relevant_docs_multi)} 个相关文档\")\n", "for i, doc in enumerate(relevant_docs_multi, 1):\n", "    # 显示每个文档的前80个字符\n", "    preview = doc[:80] + \"...\" if len(doc) > 80 else doc\n", "    print(f\"   {i}. {preview}\")\n", "\n", "# 基于多个文档生成综合答案\n", "# Generate comprehensive answer based on multiple documents\n", "print(f\"\\n🤖 正在基于 {len(relevant_docs_multi)} 个文档生成综合答案...\")\n", "answer_multi = rag.generate_answer(query_multi, relevant_docs_multi)\n", "print(f\"\\n💬 综合答案: {answer_multi}\")\n", "\n", "# ========================================\n", "# 测试2: 不相关查询处理能力\n", "# Test 2: Irrelevant Query Handling Capability\n", "# ========================================\n", "\n", "print(\"\\n\\n🧪 测试2: 不相关查询处理 / Irrelevant Query Handling Test\")\n", "print(\"-\" * 50)\n", "print(\"📝 目标: 测试系统对超出知识库范围查询的处理能力\")\n", "print(\"📝 Goal: Test system's handling of queries outside knowledge base scope\")\n", "\n", "# 提出一个与文档内容完全不相关的查询\n", "# Ask a query completely unrelated to document content\n", "irrelevant_query = \"今天天气怎么样？\"\n", "print(f\"\\n🔍 不相关查询: {irrelevant_query}\")\n", "print(f\"🎯 预期: 系统应该识别出查询与文档不相关，并给出适当回应\")\n", "\n", "# 执行查询并分析结果\n", "# Execute query and analyze results\n", "result_irrelevant = rag.query(irrelevant_query)\n", "\n", "print(f\"\\n📊 处理结果:\")\n", "print(f\"   查询: {result_irrelevant['question']}\")\n", "print(f\"   最相关文档: {result_irrelevant['relevant_documents'][0][:80]}...\")\n", "print(f\"   系统回应: {result_irrelevant['answer']}\")\n", "\n", "# 分析系统是否正确识别了不相关查询\n", "# Analyze if system correctly identified irrelevant query\n", "if \"没有\" in result_irrelevant['answer'] or \"不\" in result_irrelevant['answer']:\n", "    print(f\"   ✅ 系统正确识别了不相关查询\")\n", "else:\n", "    print(f\"   ⚠️ 系统可能没有完全识别查询的不相关性\")\n", "\n", "# ========================================\n", "# 测试3: 系统配置和性能信息\n", "# Test 3: System Configuration and Performance Info\n", "# ========================================\n", "\n", "print(\"\\n\\n📊 测试3: 系统配置和性能信息 / System Configuration and Performance Info\")\n", "print(\"-\" * 50)\n", "\n", "# 收集和显示系统配置信息\n", "# Collect and display system configuration information\n", "print(f\"\\n🔧 核心配置:\")\n", "print(f\"   🤖 语言模型: 阿里云通义千问 (qwen-plus-2025-01-25)\")\n", "print(f\"   🔢 嵌入模型: Jina Embeddings\")\n", "print(f\"   📚 文档数量: {len(rag.docs)}\")\n", "\n", "# 计算嵌入向量维度\n", "# Calculate embedding vector dimensions\n", "embedding_dim = len(rag.doc_embeddings[0]) if rag.doc_embeddings else 'N/A'\n", "print(f\"   📐 嵌入维度: {embedding_dim}\")\n", "print(f\"   🔍 检索方法: 余弦相似度计算\")\n", "\n", "print(f\"\\n⚡ 性能特性:\")\n", "print(f\"   ✅ 多文档检索: 支持top-k检索\")\n", "print(f\"   ✅ 中文问答: 完全支持中文查询和回答\")\n", "print(f\"   ✅ 语义搜索: 基于向量相似度的语义理解\")\n", "print(f\"   ✅ 实时生成: 动态生成个性化答案\")\n", "print(f\"   ✅ 错误处理: 能够处理不相关查询\")\n", "\n", "# 计算一些基本统计信息\n", "# Calculate some basic statistics\n", "total_chars = sum(len(doc) for doc in rag.docs)\n", "avg_doc_length = total_chars / len(rag.docs)\n", "print(f\"\\n📈 文档统计:\")\n", "print(f\"   📄 总字符数: {total_chars:,}\")\n", "print(f\"   📏 平均文档长度: {avg_doc_length:.1f} 字符\")\n", "print(f\"   🎯 覆盖领域: 物理学、化学、生物学、计算机科学\")\n", "\n", "# ========================================\n", "# 高级功能演示完成\n", "# Advanced features demo completion\n", "# ========================================\n", "\n", "print(\"\\n\" + \"=\" * 70)\n", "print(\"🎉 高级功能演示完成！RAG系统表现优秀\")\n", "print(\"🎉 Advanced features demo completed! RAG system performs excellently\")\n", "print(\"=\" * 70)\n", "\n", "print(f\"\\n🏆 演示总结:\")\n", "print(f\"   ✅ 多文档检索: 成功检索并综合多个相关文档\")\n", "print(f\"   ✅ 智能回答: 能够基于检索内容生成准确答案\")\n", "print(f\"   ✅ 边界处理: 正确处理超出知识范围的查询\")\n", "print(f\"   ✅ 系统稳定: 配置正确，性能良好\")\n", "\n", "print(f\"\\n🚀 系统已准备好进行正式评估测试\")\n", "print(f\"🚀 System is ready for formal evaluation testing\")"]}, {"cell_type": "markdown", "id": "2e8dbd81", "metadata": {}, "source": ["收集评估数据\n", "为了收集评估数据，我们首先需要一组用于运行 RAG 的查询。我们可以通过 RAG 系统运行查询，并为每个查询收集 response 和 retrieved_contexts。您也可以选择为每个查询准备一组“黄金答案”来评估系统的性能。"]}, {"cell_type": "code", "execution_count": 6, "id": "fbf7c2b6", "metadata": {}, "outputs": [], "source": ["# 定义测试查询集合\n", "# Define test query collection\n", "# 这些查询用于评估RAG系统的性能，涵盖了不同类型的科学问题\n", "sample_queries = [\n", "    \"Who introduced the theory of relativity?\",      # 物理学问题 - 相对论\n", "    \"Who was the first computer programmer?\",        # 计算机科学问题 - 编程历史\n", "    \"What did <PERSON> contribute to science?\",  # 物理学问题 - 牛顿贡献\n", "    \"Who won two Nobel Prizes for research on radioactivity?\",  # 化学/物理问题 - 诺贝尔奖\n", "    \"What is the theory of evolution by natural selection?\"      # 生物学问题 - 进化论\n", "]\n", "\n", "# 定义期望的参考答案\n", "# Define expected reference answers\n", "# 这些是\"黄金标准\"答案，用于评估RAG系统生成答案的质量\n", "expected_responses = [\n", "    \"<PERSON> proposed the theory of relativity, which transformed our understanding of time, space, and gravity.\",\n", "    \"<PERSON> is regarded as the first computer programmer for her work on <PERSON>'s early mechanical computer, the Analytical Engine.\",\n", "    \"<PERSON> formulated the laws of motion and universal gravitation, laying the foundation for classical mechanics.\",\n", "    \"<PERSON> was a physicist and chemist who conducted pioneering research on radioactivity and won two Nobel Prizes.\",\n", "    \"<PERSON> introduced the theory of evolution by natural selection in his book 'On the Origin of Species'.\"\n", "]"]}, {"cell_type": "code", "execution_count": 7, "id": "4c81cb58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在检索与查询相关的文档: 'Who introduced the theory of relativity?'\n", "Retrieving documents relevant to query: 'Who introduced the theory of relativity?'\n", "找到 1 个相关文档\n", "Found 1 relevant documents\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n", "正在检索与查询相关的文档: 'Who was the first computer programmer?'\n", "Retrieving documents relevant to query: 'Who was the first computer programmer?'\n", "找到 1 个相关文档\n", "Found 1 relevant documents\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n", "正在检索与查询相关的文档: 'What did <PERSON> contribute to science?'\n", "Retrieving documents relevant to query: 'What did <PERSON> contribute to science?'\n", "找到 1 个相关文档\n", "Found 1 relevant documents\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n", "正在检索与查询相关的文档: 'Who won two Nobel Prizes for research on radioactivity?'\n", "Retrieving documents relevant to query: 'Who won two Nobel Prizes for research on radioactivity?'\n", "找到 1 个相关文档\n", "Found 1 relevant documents\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n", "正在检索与查询相关的文档: 'What is the theory of evolution by natural selection?'\n", "Retrieving documents relevant to query: 'What is the theory of evolution by natural selection?'\n", "找到 1 个相关文档\n", "Found 1 relevant documents\n", "正在生成答案... / Generating answer...\n", "答案生成完成 / Answer generation completed\n"]}], "source": ["# 创建评估数据集\n", "# Create evaluation dataset\n", "# 这个数据集将包含查询、检索到的文档、生成的回答和参考答案\n", "dataset = []\n", "\n", "# 遍历每个查询和对应的参考答案\n", "# Iterate through each query and corresponding reference answer\n", "for query, reference in zip(sample_queries, expected_responses):\n", "    \n", "    # 步骤1: 使用RAG系统检索最相关的文档\n", "    # Step 1: Retrieve most relevant documents using RAG system\n", "    relevant_docs = rag.get_most_relevant_docs(query)\n", "    \n", "    # 步骤2: 基于检索到的文档生成答案\n", "    # Step 2: Generate answer based on retrieved documents\n", "    response = rag.generate_answer(query, relevant_docs)\n", "    \n", "    # 步骤3: 将结果添加到数据集中\n", "    # Step 3: Add results to dataset\n", "    dataset.append(\n", "        {\n", "            \"user_input\": query,              # 用户输入的查询\n", "            \"retrieved_contexts\": relevant_docs,  # 检索到的相关文档\n", "            \"response\": response,             # RAG系统生成的回答\n", "            \"reference\": reference            # 参考答案（黄金标准）\n", "        }\n", "    )\n", "\n", "# 打印数据集创建完成信息\n", "print(f\"评估数据集创建完成，包含 {len(dataset)} 个样本\")\n", "print(f\"Evaluation dataset created with {len(dataset)} samples\")"]}, {"cell_type": "code", "execution_count": 8, "id": "9d0777b4", "metadata": {}, "outputs": [], "source": ["# 导入RAGAS评估数据集类\n", "# Import RAGAS evaluation dataset class\n", "from ragas import EvaluationDataset\n", "\n", "# 将Python列表转换为RAGAS评估数据集格式\n", "# Convert Python list to RAGAS evaluation dataset format\n", "# 这个格式是RAGAS库进行评估所需要的标准格式\n", "evaluation_dataset = EvaluationDataset.from_list(dataset)\n", "\n", "print(\"评估数据集格式转换完成\")\n", "print(\"Evaluation dataset format conversion completed\")\n", "print(f\"数据集大小: {len(evaluation_dataset)} 个样本\")\n", "print(f\"Dataset size: {len(evaluation_dataset)} samples\")"]}, {"cell_type": "markdown", "id": "17637d24", "metadata": {}, "source": ["## RAG系统性能评估 / RAG System Performance Evaluation\n", "\n", "我们已经成功收集了评估数据。现在，我们可以使用一组常用的 RAG 评估指标，在收集的数据集上评估我们的 RAG 系统。\n", "\n", "### 评估指标说明：\n", "\n", "1. **Context Recall (上下文召回率)**：\n", "   - 衡量检索到的文档中包含答案所需信息的比例\n", "   - 值越高表示检索质量越好\n", "   - 范围：0-1，1为最佳\n", "\n", "2. **Faithfulness (忠实度)**：\n", "   - 衡量生成的答案与检索到的文档内容的一致性\n", "   - 值越高表示答案越忠实于源文档\n", "   - 范围：0-1，1为最佳\n", "\n", "3. **Factual Correctness (事实正确性)**：\n", "   - 衡量生成答案的事实准确性\n", "   - 通过F1分数计算\n", "   - 范围：0-1，1为最佳\n", "\n", "您可以选择任何模型作为评估 LLM 进行评估。我们将使用相同的通义千问模型来确保一致性。"]}, {"cell_type": "code", "execution_count": 9, "id": "39b5df66", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "64783e7055b341f49969d9be3a5b5cea", "version_major": 2, "version_minor": 0}, "text/plain": ["Evaluating:   0%|          | 0/15 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'context_recall': 1.0000, 'faithfulness': 0.8750, 'factual_correctness(mode=f1)': 0.6340}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 导入RAGAS评估相关模块\n", "# Import RAGAS evaluation related modules\n", "from ragas import evaluate                    # 主评估函数\n", "from ragas.llms import LangchainLLMWrapper   # LangChain LLM包装器\n", "from ragas.metrics import LLMContextRecall, Faithfulness, FactualCorrectness  # 评估指标\n", "\n", "print(\"开始RAG系统性能评估...\")\n", "print(\"Starting RAG system performance evaluation...\")\n", "\n", "# 创建评估用的LLM包装器\n", "# Create LLM wrapper for evaluation\n", "# 使用相同的通义千问模型来确保评估的一致性\n", "evaluator_llm = LangchainLLMWrapper(llm)\n", "print(f\"评估模型: {llm.model_name}\")\n", "\n", "# 定义要使用的评估指标\n", "# Define evaluation metrics to use\n", "metrics = [\n", "    LLMContextRecall(),      # 上下文召回率：检索质量\n", "    Faithfulness(),          # 忠实度：答案与文档的一致性\n", "    FactualCorrectness()     # 事实正确性：答案的准确性\n", "]\n", "\n", "print(f\"使用评估指标: {[metric.__class__.__name__ for metric in metrics]}\")\n", "print(\"正在进行评估，请稍候...\")\n", "print(\"Evaluation in progress, please wait...\")\n", "\n", "# 执行评估\n", "# Perform evaluation\n", "# 这将对每个样本计算所有指定的评估指标\n", "result = evaluate(\n", "    dataset=evaluation_dataset,  # 评估数据集\n", "    metrics=metrics,            # 评估指标列表\n", "    llm=evaluator_llm          # 评估用的语言模型\n", ")\n", "\n", "print(\"\\n评估完成！/ Evaluation completed!\")\n", "print(\"评估结果 / Evaluation Results:\")\n", "result"]}, {"cell_type": "code", "execution_count": null, "id": "result_analysis", "metadata": {}, "outputs": [], "source": ["# 评估结果分析和解释\n", "# Evaluation result analysis and interpretation\n", "\n", "print(\"=\" * 60)\n", "print(\"RAG系统评估结果详细分析 / Detailed Analysis of RAG System Evaluation\")\n", "print(\"=\" * 60)\n", "\n", "# 提取评估结果\n", "# Extract evaluation results\n", "context_recall = result.get('context_recall', 0)\n", "faithfulness = result.get('faithfulness', 0)\n", "factual_correctness = result.get('factual_correctness(mode=f1)', 0)\n", "\n", "print(f\"\\n📊 评估指标详细解读:\")\n", "print(f\"📊 Detailed Interpretation of Evaluation Metrics:\")\n", "print(\"-\" * 50)\n", "\n", "# 上下文召回率分析\n", "# Context recall analysis\n", "print(f\"\\n1. 上下文召回率 (Context Recall): {context_recall:.4f}\")\n", "if context_recall >= 0.9:\n", "    print(\"   ✅ 优秀 - 检索系统能够找到包含答案的相关文档\")\n", "    print(\"   ✅ Excellent - Retrieval system can find relevant documents containing answers\")\n", "elif context_recall >= 0.7:\n", "    print(\"   ✅ 良好 - 检索质量较好，大部分查询能找到相关文档\")\n", "    print(\"   ✅ Good - Good retrieval quality, most queries find relevant documents\")\n", "elif context_recall >= 0.5:\n", "    print(\"   ⚠️ 一般 - 检索质量中等，需要优化检索策略\")\n", "    print(\"   ⚠️ Average - Medium retrieval quality, need to optimize retrieval strategy\")\n", "else:\n", "    print(\"   ❌ 较差 - 检索系统需要改进\")\n", "    print(\"   ❌ Poor - Retrieval system needs improvement\")\n", "\n", "# 忠实度分析\n", "# Faithfulness analysis\n", "print(f\"\\n2. 忠实度 (Faithfulness): {faithfulness:.4f}\")\n", "if faithfulness >= 0.9:\n", "    print(\"   ✅ 优秀 - 生成的答案高度忠实于检索到的文档\")\n", "    print(\"   ✅ Excellent - Generated answers are highly faithful to retrieved documents\")\n", "elif faithfulness >= 0.7:\n", "    print(\"   ✅ 良好 - 答案基本忠实于源文档\")\n", "    print(\"   ✅ Good - Answers are generally faithful to source documents\")\n", "elif faithfulness >= 0.5:\n", "    print(\"   ⚠️ 一般 - 有时会偏离源文档内容\")\n", "    print(\"   ⚠️ Average - Sometimes deviates from source document content\")\n", "else:\n", "    print(\"   ❌ 较差 - 经常产生与文档不符的内容\")\n", "    print(\"   ❌ Poor - Often generates content inconsistent with documents\")\n", "\n", "# 事实正确性分析\n", "# Factual correctness analysis\n", "print(f\"\\n3. 事实正确性 (Factual Correctness): {factual_correctness:.4f}\")\n", "if factual_correctness >= 0.8:\n", "    print(\"   ✅ 优秀 - 答案事实准确性很高\")\n", "    print(\"   ✅ Excellent - Very high factual accuracy in answers\")\n", "elif factual_correctness >= 0.6:\n", "    print(\"   ✅ 良好 - 答案大部分事实正确\")\n", "    print(\"   ✅ Good - Most facts in answers are correct\")\n", "elif factual_correctness >= 0.4:\n", "    print(\"   ⚠️ 一般 - 事实准确性中等，需要改进\")\n", "    print(\"   ⚠️ Average - Medium factual accuracy, needs improvement\")\n", "else:\n", "    print(\"   ❌ 较差 - 事实错误较多\")\n", "    print(\"   ❌ Poor - Many factual errors\")\n", "\n", "# 综合评估\n", "# Overall assessment\n", "overall_score = (context_recall + faithfulness + factual_correctness) / 3\n", "print(f\"\\n🎯 综合评分 (Overall Score): {overall_score:.4f}\")\n", "print(f\"🎯 Overall Score: {overall_score:.4f}\")\n", "\n", "if overall_score >= 0.8:\n", "    print(\"\\n🎉 RAG系统表现优秀！\")\n", "    print(\"🎉 RAG system performs excellently!\")\n", "elif overall_score >= 0.6:\n", "    print(\"\\n👍 RAG系统表现良好\")\n", "    print(\"👍 RAG system performs well\")\n", "else:\n", "    print(\"\\n🔧 RAG系统需要进一步优化\")\n", "    print(\"🔧 RAG system needs further optimization\")\n", "\n", "# 改进建议\n", "# Improvement suggestions\n", "print(f\"\\n💡 改进建议 / Improvement Suggestions:\")\n", "print(\"-\" * 50)\n", "\n", "if context_recall < 0.8:\n", "    print(\"• 优化文档分块策略，提高检索精度\")\n", "    print(\"• Optimize document chunking strategy to improve retrieval accuracy\")\n", "    \n", "if faithfulness < 0.8:\n", "    print(\"• 改进提示词，强调基于文档内容回答\")\n", "    print(\"• Improve prompts to emphasize answering based on document content\")\n", "    \n", "if factual_correctness < 0.7:\n", "    print(\"• 增加更高质量的文档数据\")\n", "    print(\"• Add higher quality document data\")\n", "    print(\"• 考虑使用更强的语言模型\")\n", "    print(\"• Consider using more powerful language models\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"评估分析完成 / Evaluation analysis completed\")\n", "print(\"=\" * 60)"]}], "metadata": {"kernelspec": {"display_name": "pytorch_is", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}