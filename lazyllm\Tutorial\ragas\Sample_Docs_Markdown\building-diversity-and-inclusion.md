---
title: "Building an Inclusive Remote Culture"
description: "We are passionate about all remote working and enabling an inclusive work environment."
---

We are passionate about [all remote working]({{< ref "remote-vision" >}}) and enabling an [inclusive work]({{< ref "inclusion#fully-distributed-and-completely-connected" >}}) environment. There isn't one big activity we can take to accomplish this. Instead, it is a mix of numerous [activities and behaviors](https://about.gitlab.com/blog/2019/12/06/how-all-remote-supports-inclusion-and-bolsters-communities/) combined to enable our team members to feel they belong in GitLab.

Those activities and behaviors include:

- [DIB Events]({{< ref "diversity-and-inclusion-events" >}})
- [DIB Advisory Group]({{< ref "inclusion#global-diversity-inclusion--belonging-advisory-group" >}})
- [Diversity, Inclusion & Belonging  TMRGs - Team Member Resource Groups]({{< ref "inclusion#ergs---employee-resource-groups" >}})
- [Our Values]({{< ref "values" >}})
- [Family and Friends first, Work second]({{< ref "values#family-and-friends-first-work-second" >}})
- [Inclusive language and pronouns]({{< ref "values#inclusive-language--pronouns" >}})
- [Parental leave]({{< ref "general-and-entity-benefits#parental-leave" >}})
- [Asynchronous communication and workflows](/handbook/company/culture/all-remote/asynchronous)

## Tips for Companies

### Defining Diversity, Inclusion & Belonging

A great place to begin is to set a foundation of the basic understanding of how your company defines these terms. An example is GitLab's [Diversity, Inclusion & Belonging  value]({{< ref "values#diversity-inclusion" >}}), supported by an evolving list of operating principles.

In other places, you may hear them used interchangeably. Understanding their differences is essential to driving the initiatives. As an example, you may hire many diverse candidates, but if you don't create an inclusive environment the work can be in vain.

### Evaluating the company's current DIB landscape

Consider what you are already doing in this space.

- What is the current feedback?
- What are team members saying with company engagement surveys?
- What are the goals you are wanting to achieve?
- What are the metrics saying?

#### Diversity, Inclusion & Belonging survey

A company survey is a great way to get a sense of team members' thoughts and concerns. The DIB team at GitLab runs an annual survey via CultureAmp to gauge where we are as a company in terms of diversity, inclusion & belonging. We take the results and implement projects and solutions based on insights from the survey results.

### Naming this body of work

Although Diversity, Inclusion & Belonging are often understood globally, there are other terms that can be leveraged to name your efforts. The naming should be unique to your company. Examples could include Belonging, Inclusion & Collaborations, etc.

### Developing a mission statement

When creating a diverse and inclusive culture, most companies will develop a mission statement to support their vision. Your mission statement should articulate the purpose of your strategy. In a few sentences, you should be able to succinctly provide the why and the how. Be sure to take into account your company’s current overall mission and vision. It is best to align your DIB mission and vision with your organization’s overarching mission and vision. To do this, you may consider how your DIB Strategy can build on, scale, or enhance the organization’s mission and vision.

### Creating TMRGs

In general, TMRGs are an excellent support system and key to providing awareness, respect, and building diversity, inclusion & belonging within the workplace. These groups are a proven way to increase cultural competency, retention of team members, provide marketplace insights to the business, attract diverse talent, and more. The endorsement of TMRGs allows team members to identify common interests and decide how they can be shared with others. When creating TMRGs there are a few initial steps to consider:

- [Creating guidelines to help support TMRGs being stood up within your company]({{< ref "erg-guide" >}})
    - Naming of each TMRG
    - Roles within each TMRG
    - Aligning to company strategy
- Creating forms, Google groups, ways of tracking attendance for TMRG events, and membership metrics

### Creating a Diversity, Inclusion & Belonging  Advisory Group

Consider creating this group as your highest level of global voices: a team of company influencers who can be instrumental in driving DIB efforts from a global perspective. How do you do this? GitLab conducted a global "All Call" for those who would be interested in joining and advised them to provide "why" DIB was important to them, along with other questions such as division, location, etc. When we were reviewing we were able to have the best possible outcome of representation across the globe. Additional support in sustaining the group would be:

- [DIB Advisory group guidelines]({{< ref "influencer-group-guide" >}})
- Appointing an Executive sponsor from the company
- Designating leads of the group
- Deciding on when it is time to enact or rotate the opportunity for new advisory group members

### More to come

- DIB Initiatives
- DIB Awards for Recognition
- DIB Surveys
- DIB Framework Inclusive Benefits

## Tips for Managers

### Set aside time to show up for planned DIB events

You may be surprised by how much seeing your face in these events validates this as a worthwhile use of time which is valued by the company. Seeing you in attendance also opens the door to future conversations that might begin with: "I saw you in XYZ meeting, what did you think of such-and-such topic?"

### Incorporate DIB into your team meetings

Connecting to team members is key to understand who they are and what they bring to the team. A great initial step is to start with open discussions. You could start by opening your next team meeting to chat about what they feel inclusion looks like, what is important to them as a team to feel included, etc. This could then move into monthly or quarterly team DIB icebreakers, trainings, etc. The idea is to make sure DIB is not touched on once and never mentioned again, but more of an understood aspect of your team environment. You can do this by setting and communicating DIB goals for your team, and sharing how we will measure success (an idea could be sharing DIB survey data with team members).

### Encourage Diversity of Thought and Speaking Up

Don’t assume you know more than others. Give people a chance to add their perspective or expertise. Teach people how to disagree, set the expectation that it is OK to disagree, and encourage people to do it day-to-day. Celebrate diverse thoughts and thank people for making contributions, particularly if they disagree with you. It takes [courage to disagree publicly with a leader]({{< ref "values#see-something-say-something" >}}).

### Connect with your team members

To help team members feel comfortable being themselves, leaders should consider authentic ways to connect to their team members. Everyone wants to feel visible and included in order to perform their best work. Being visible usually includes being seen for accomplishments, acknowledgment of contributions, what is the same, and what differs from team member to team member. Leaders and all team members should show an interest in and respect for differences and contributions.

Considerations could be that of language/terminology such as “spouses” or “partners” instead of making assumptions about team members sexual orientation.  Being considerate of dietary restrictions when choosing food options for Contribute or other local gatherings.  Acknowledging birthdays, recalling personal things that might have been mentioned in past calls such as moving to a new home, an ill family member or team member, and following up authentically.

For more information see our [Inclusive Language.](https://docs.google.com/presentation/d/186RK9QqOYxF8BmVS15AOKvwFpt4WglKKDR7cUCeDGkE/edit?usp=sharing)

### Ask employees what pronouns they use

Pronouns are a large piece of a person's identity and are often used to communicate a person’s gender, which is why it is so important to get it right. Asking for a person's pronouns and using those pronouns consistently shows that you respect their identity, but it also helps to create a more welcoming, safe, and supportive environment where people can feel comfortable being themselves. This is a change that goes a long way to foster inclusion.

### Be mindful of the times of your meetings

It's important to consider global times and alternating meetings to accommodate regions. Be mindful of families, commitments, observances, holidays, and meeting times that may be out of team members' working hours. Every meeting time won't be perfect for everyone (which is why all meetings [have an agenda](/handbook/company/culture/all-remote/meetings#have-an-agenda)), but making a conscious effort to alternate times is to ensure the same people aren't being excluded. For more, view our [Inclusive Meetings]({{< ref "values#inclusive-meetings" >}}) operating principle.

### Be a Role Model

As a manager, you are in a unique position to model good DIB practices for the rest of your team. **Be authentic, own up to mistakes, and commit to doing better next time. Don’t dwell on it.** Set a good example by admitting that you make mistakes and invite people to help you and each other by speaking up in accordance with our feedback guidelines. By holding each other accountable, we get better as a team.

## Tips for Team Members

### Schedule a Coffee Chat with a Team Member

Understanding GitLab is [fully remote](/handbook/company/culture/all-remote/terminology), there is an opportunity to get to know team members beyond your geographical location as well as outside of your division. This provides an opportunity to:

- learn more about other cultures and work divisions
- cultivate better global communication
- make progress toward building an inclusive environment

[Coffee chats](/handbook/company/culture/all-remote/informal-communication#coffee-chats) are suggested during onboarding but you don't need to stop there. It is recommended to continue this action whenever you would like to. Please take a look at our [GitLab team page](/handbook/company/team/) and feel free to select someone for a coffee chat!

### Hold Each Other Accountable

Hold each other accountable and speak up when someone uses non-inclusive language. We should also celebrate when we are inclusive, and strive to support each other every day.

### Avoid Identity Assumption Statements

Identity is a personal thing. Looks or names can imply diverse attributes, but they are not perfect identity indicators. Avoid making statements about someone's perceived race, gender, age, ethnicity, or other personal characteristics. When people are typecast or feel misunderstood, they are less likely to feel that they belong.

### Tip to Uncover Unconscious Bias

When you are dealing with a specific situation, you can mentally flip the situation around and see how that feels. For example, flip it from a woman to a man, and if it feels off then you might have a bias. By putting ourselves in someone else's shoes, we can better understand their point of view and be more supportive.

### More to come

- Consider joining a [TMRG]({{< ref "erg-guide" >}}) (Team Member Resource Group)
- Support others as an Ally
- Review the [Diversity, Inclusion & Belonging]({{< ref "inclusion" >}}) page
