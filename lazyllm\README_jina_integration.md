# Jina AI 嵌入模块集成指南

本文档说明如何在 LazyLLM 中使用自定义的 Jina AI 嵌入模块。

## 📋 概述

我们已经成功将您的 `OnlineEmbeddingModule()` 替换为自定义的 Jina AI 嵌入模块，该模块：

- ✅ 符合 OpenAI 格式标准
- ✅ 支持 Jina AI 的 `jina-embeddings-v3` 模型
- ✅ 包含 `text-matching` 任务类型
- ✅ 使用您提供的 API Key
- ✅ 完全兼容 LazyLLM 的 Document 和 Retriever 系统

## 🔧 修改内容

### 1. 主要文件修改

**文件**: `chat_rag_demo.py`

**修改前**:
```python
documents = lazyllm.Document(dataset_path="/path/to/your/doc/dir",
                             embed=lazyllm.OnlineEmbeddingModule(),
                             manager=False)
```

**修改后**:
```python
# 自定义 Jina AI 嵌入模块
class JinaEmbeddingModule(OnlineEmbeddingModuleBase):
    # ... 完整实现 ...

# 创建实例
jina_embedding = JinaEmbeddingModule(
    api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
    model_name="jina-embeddings-v3",
    task="text-matching"
)

documents = lazyllm.Document(dataset_path="/path/to/your/doc/dir",
                             embed=jina_embedding,
                             manager=False)
```

### 2. 核心特性

#### API 请求格式
```json
{
    "model": "jina-embeddings-v3",
    "input": ["your text here"],
    "task": "text-matching"
}
```

#### 响应解析
自动解析 OpenAI/Jina AI 格式的响应：
```json
{
    "data": [
        {"embedding": [0.1, 0.2, 0.3, ...]},
        ...
    ]
}
```

## 🚀 使用方法

### 1. 基本使用

```python
import lazyllm
from lazyllm.module import OnlineEmbeddingModuleBase

# 创建 Jina AI 嵌入模块
jina_embedding = JinaEmbeddingModule(
    api_key="your-jina-api-key",
    model_name="jina-embeddings-v3",
    task="text-matching"
)

# 在 Document 中使用
documents = lazyllm.Document(
    dataset_path="/path/to/your/documents",
    embed=jina_embedding,
    manager=False
)
```

### 2. 与 RAG 系统集成

```python
# 创建检索器
retriever = lazyllm.Retriever(
    doc=documents,
    group_name="CoarseChunk",
    similarity="bm25_chinese",
    topk=3
)

# 使用检索器
query = "您的查询问题"
doc_node_list = retriever(query=query)
```

### 3. 参数配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `api_key` | str | 必需 | Jina AI 的 API 密钥 |
| `model_name` | str | "jina-embeddings-v3" | 嵌入模型名称 |
| `task` | str | "text-matching" | 任务类型 |

## 🧪 测试

### 运行测试脚本

```bash
cd lazyllm
python test_jina_embedding.py
```

测试脚本将验证：
- ✅ 单个文本嵌入
- ✅ 批量文本嵌入
- ✅ 多语言文本处理
- ✅ 与 LazyLLM 的集成

### 预期输出

```
🚀 开始测试 Jina AI 嵌入模块
==================================================
📝 测试文本:
1. Organic skincare for sensitive skin with aloe...
2. Bio-Hautpflege für empfindliche Haut mit Aloe...
...

🔧 测试单个文本嵌入...
✅ 单个文本嵌入成功!
   嵌入维度: 1024
   前5个值: [0.1234, -0.5678, ...]
   数据类型: <class 'list'>

🎉 所有测试通过!
```

## 🔍 故障排除

### 常见问题

1. **API Key 错误**
   ```
   错误: 401 Unauthorized
   解决: 检查 API Key 是否正确
   ```

2. **网络连接问题**
   ```
   错误: Connection timeout
   解决: 检查网络连接和防火墙设置
   ```

3. **模型名称错误**
   ```
   错误: Model not found
   解决: 确认使用正确的模型名称 "jina-embeddings-v3"
   ```

### 调试模式

在 `JinaEmbeddingModule` 中添加调试信息：

```python
def _encapsulated_data(self, text: str, **kwargs) -> Dict:
    json_data = {
        "model": self._embed_model_name,
        "input": [text] if isinstance(text, str) else text,
        "task": self.task
    }
    
    # 调试信息
    print(f"🔍 请求数据: {json_data}")
    
    return json_data
```

## 📚 相关文档

- [Jina AI API 文档](https://docs.jina.ai/)
- [LazyLLM 官方文档](https://lazyllm.readthedocs.io/)
- [OpenAI 嵌入 API 格式](https://platform.openai.com/docs/api-reference/embeddings)

## 🎯 下一步

1. **测试集成**: 运行 `test_jina_embedding.py` 验证功能
2. **配置文档路径**: 将 `/path/to/your/doc/dir` 替换为实际路径
3. **运行 RAG 系统**: 执行 `chat_rag_demo.py` 开始使用
4. **性能优化**: 根据需要调整批处理大小和超时设置

## ✨ 优势

- 🚀 **高性能**: Jina AI 的先进嵌入模型
- 🌍 **多语言**: 支持多种语言的文本嵌入
- 🔧 **易集成**: 完全兼容 LazyLLM 生态系统
- 📊 **高质量**: 优秀的语义理解能力

---

**注意**: 请确保您的 API Key 安全，不要在公共代码库中暴露。
