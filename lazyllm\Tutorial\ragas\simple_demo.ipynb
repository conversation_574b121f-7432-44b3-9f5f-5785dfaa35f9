{"cells": [{"cell_type": "code", "execution_count": 2, "id": "f9468324", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.13718598426177148"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# 导入 RAGAS 库中的核心组件\n", "# SingleTurnSample: 用于创建单轮对话样本的数据结构\n", "# BleuScore: BLEU 评分指标，用于评估文本生成质量\n", "from ragas import SingleTurnSample\n", "from ragas.metrics import BleuScore\n", "\n", "# 创建测试数据字典，包含三个关键字段：\n", "test_data = {\n", "    # user_input: 用户输入的原始文本和任务指令\n", "    # 这里的任务是要求对给定文本进行摘要\n", "    \"user_input\": \"summarise given text\\nThe company reported an 8% rise in Q3 2024, driven by strong performance in the Asian market. Sales in this region have significantly contributed to the overall growth. Analysts attribute this success to strategic marketing and product localization. The positive trend in the Asian market is expected to continue into the next quarter.\",\n", "    \n", "    # response: AI 模型生成的响应/摘要\n", "    # 这是需要被评估的目标文本\n", "    \"response\": \"The company experienced an 8% increase in Q3 2024, largely due to effective marketing strategies and product adaptation, with expectations of continued growth in the coming quarter.\",\n", "    \n", "    # reference: 参考答案/标准答案\n", "    # 用作评估 response 质量的基准\n", "    \"reference\": \"The company reported an 8% growth in Q3 2024, primarily driven by strong sales in the Asian market, attributed to strategic marketing and localized products, with continued growth anticipated in the next quarter.\"\n", "}\n", "\n", "# 初始化 BLEU 评分指标\n", "# BLEU (Bilingual Evaluation Understudy) 是一种常用的文本质量评估指标\n", "# 主要通过计算 n-gram 重叠度来评估生成文本与参考文本的相似性\n", "metric = BleuScore()\n", "\n", "# 将测试数据转换为 SingleTurnSample 对象\n", "# 这是 RAGAS 库要求的标准数据格式\n", "test_data = SingleTurnSample(**test_data)\n", "\n", "# 计算单轮对话的 BLEU 评分\n", "# 返回值是一个 0-1 之间的浮点数，越接近 1 表示质量越好\n", "# 这里的结果约为 0.137，表明生成的摘要与参考答案有一定相似性但还有改进空间\n", "metric.single_turn_score(test_data)"]}, {"cell_type": "markdown", "id": "4a2b81d1", "metadata": {}, "source": ["# 使用llm 进行评估"]}, {"cell_type": "code", "execution_count": 2, "id": "991a7d45", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "llm = ChatOpenAI(openai_api_base=\"https://dashscope.aliyuncs.com/compatible-mode/v1\", \n", "                 openai_api_key=\"sk-282f3112bd714d6e85540da173b5517c\", model=\"qwen-plus-2025-01-25\")"]}, {"cell_type": "code", "execution_count": 4, "id": "fe1adad3", "metadata": {}, "outputs": [], "source": ["from ragas.llms import LangchainLLMWrapper\n", "from ragas.embeddings import LangchainEmbeddingsWrapper\n", "# from langchain_openai import OpenAIEmbeddings\n", "from jina_embeddings import create_jina_embeddings\n", "evaluator_llm = LangchainLLMWrapper(llm)\n", "# evaluator_embeddings = LangchainEmbeddingsWrapper(OpenAIEmbeddings())\n", "evaluator_embeddings = LangchainEmbeddingsWrapper(create_jina_embeddings(api_key = 'jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR'))"]}, {"cell_type": "markdown", "id": "844cf6e6", "metadata": {}, "source": ["## 评估\n", "### 在这里，我们将使用 AspectCritic，这是一个基于LLM的度量，根据评估标准输出通过/失败。"]}, {"cell_type": "code", "execution_count": 5, "id": "e3c44854", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from ragas import SingleTurnSample\n", "from ragas.metrics import AspectCritic\n", "\n", "test_data = {\n", "    \"user_input\": \"summarise given text\\nThe company reported an 8% rise in Q3 2024, driven by strong performance in the Asian market. Sales in this region have significantly contributed to the overall growth. Analysts attribute this success to strategic marketing and product localization. The positive trend in the Asian market is expected to continue into the next quarter.\",\n", "    \"response\": \"The company experienced an 8% increase in Q3 2024, largely due to effective marketing strategies and product adaptation, with expectations of continued growth in the coming quarter.\",\n", "}\n", "\n", "metric = AspectCritic(name=\"summary_accuracy\",llm=evaluator_llm, definition=\"Verify if the summary is accurate.\")\n", "test_data = SingleTurnSample(**test_data)\n", "await metric.single_turn_ascore(test_data)"]}, {"cell_type": "markdown", "id": "c11c93e5", "metadata": {}, "source": ["在数据集上进行评估\n", "在上面的示例中，我们仅使用单个样本来评估我们的应用。然而，仅对一个样本进行评估不足以保证结果的可信度。为了确保评估可靠，你应该向你的测试数据中添加更多测试样本。\n", "\n", "在这里，我们将从Hugging Face Hub加载一个数据集，但你可以从任何来源加载数据，例如生产日志或其他数据集。只需确保每个样本包含所选度量所需的所有属性。\n", "\n", "在我们的案例中，所需的属性是\n", "- user_input: 提供给应用的输入（此处为输入文本报告）。\n", "- response: 应用生成的输出（此处为生成的摘要）。"]}, {"cell_type": "code", "execution_count": 6, "id": "dc8599e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Features in dataset: ['user_input', 'response']\n", "Total samples in dataset: 50\n"]}], "source": ["from datasets import load_dataset\n", "from ragas import EvaluationDataset\n", "eval_dataset = load_dataset(\"explodinggradients/earning_report_summary\",split=\"train\")\n", "eval_dataset = EvaluationDataset.from_hf_dataset(eval_dataset)\n", "print(\"Features in dataset:\", eval_dataset.features())\n", "print(\"Total samples in dataset:\", len(eval_dataset))"]}, {"cell_type": "code", "execution_count": 7, "id": "812bb81e", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "38c9e864083e424fa723bfd19286e306", "version_major": 2, "version_minor": 0}, "text/plain": ["Evaluating:   0%|          | 0/50 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'summary_accuracy': 1.0000}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用数据集进行评估\n", "\n", "from ragas import evaluate\n", "\n", "results = evaluate(eval_dataset, metrics=[metric])\n", "results"]}, {"cell_type": "code", "execution_count": null, "id": "29a9b9a5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "5c31cf02", "metadata": {}, "source": ["该分数表明，在我们测试数据中的所有样本中，只有84%的摘要通过了给定的评估标准。现在，重要的是要了解为什么会这样。\n", "\n", "将样本级别的得分导出到pandas dataframe"]}, {"cell_type": "code", "execution_count": 8, "id": "4c8f6aca", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_input</th>\n", "      <th>response</th>\n", "      <th>summary_accuracy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>summarise given text\\nThe Q2 earnings report r...</td>\n", "      <td>The Q2 earnings report showed a 15% revenue in...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>summarise given text\\nIn 2023, North American ...</td>\n", "      <td>Companies are strategizing to adapt to market ...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>summarise given text\\nIn 2022, European expans...</td>\n", "      <td>Many companies experienced a notable 15% growt...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>summarise given text\\nSupply chain challenges ...</td>\n", "      <td>Supply chain challenges in North America, caus...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>summarise given text\\nIn Q2 2023, the company ...</td>\n", "      <td>The company experienced a notable increase in ...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>summarise given text\\nIn 2023, marketing campa...</td>\n", "      <td>In 2023, marketing campaigns in North America ...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>summarise given text\\nThe company's internatio...</td>\n", "      <td>The company's international expansion strategy...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>summarise given text\\nIn 2024, companies are i...</td>\n", "      <td>Companies are using data analytics to customiz...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>summarise given text\\nIn 2023, logistics inves...</td>\n", "      <td>Driven by technological and infrastructural ad...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>summarise given text\\nIn 2023, the company exp...</td>\n", "      <td>The company faced challenges due to competitio...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>summarise given text\\nThe company reported a 5...</td>\n", "      <td>The company faced challenges in the European m...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>summarise given text\\nThe company reported a s...</td>\n", "      <td>The company's significant profit in Q3 2024, d...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>summarise given text\\nThe global market has ex...</td>\n", "      <td>The recent downturn has raised concerns among ...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>summarise given text\\nThe logistics industry i...</td>\n", "      <td>The industry is expected to grow by 20% in 202...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>summarise given text\\nThe company reported an ...</td>\n", "      <td>The company experienced an 8% increase in Q3 2...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>summarise given text\\nIn 2022, the Asian marke...</td>\n", "      <td>In 2022, the Asian market experienced a signif...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>summarise given text\\nThe global market has wi...</td>\n", "      <td>The global market experienced a 10% increase i...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>summarise given text\\nThe company reported a 1...</td>\n", "      <td>The company experienced significant growth due...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>summarise given text\\nThe company's revenue sa...</td>\n", "      <td>The company's financial success was significan...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>summarise given text\\nThe Marketing team is st...</td>\n", "      <td>The team is strategizing to address the challe...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>summarise given text\\nIn 2023, the global mark...</td>\n", "      <td>In 2023, the global market saw a 5% sales decr...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>summarise given text\\nIn 2022, there was an 8%...</td>\n", "      <td>Economic factors led to increased expenses, pr...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>summarise given text\\nIn 2022, the global mark...</td>\n", "      <td>In 2022, a remarkable 20% growth significantly...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>summarise given text\\nIn 2022, the European ma...</td>\n", "      <td>In 2022, the European market experienced a 5% ...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>summarise given text\\nIn 2022, companies opera...</td>\n", "      <td>In 2022, companies in Latin America faced a 15...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>summarise given text\\nIn 2023, the European ma...</td>\n", "      <td>In 2023, the European market experienced a 15%...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>summarise given text\\nThe global market is poi...</td>\n", "      <td>A significant shift is expected with a 20% gro...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>summarise given text\\nThe company reported a s...</td>\n", "      <td>The company's 8% rise in 2022 was driven by ex...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>summarise given text\\nThe logistics industry i...</td>\n", "      <td>The logistics industry is expected to face a 5...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>summarise given text\\nThe company is facing a ...</td>\n", "      <td>The company is anticipating a difficult year d...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>summarise given text\\nThe company is projectin...</td>\n", "      <td>The company's growth is fueled by strategic in...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>summarise given text\\nIn 2023, the Asian marke...</td>\n", "      <td>In 2023, the Asian market experienced a 10% in...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>summarise given text\\nThe global market has wi...</td>\n", "      <td>The global market experienced an 8% rise in Q1...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>summarise given text\\nIn 2022, there was an 8%...</td>\n", "      <td>Expenses increased across various sectors in L...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>summarise given text\\nIn 2023, companies opera...</td>\n", "      <td>In 2023, companies in Latin America faced a 15...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>summarise given text\\nSales in Latin America e...</td>\n", "      <td>Sales in Latin America saw a remarkable 20% gr...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>summarise given text\\nIn 2022, the company exp...</td>\n", "      <td>In 2022, the company faced a 5% revenue decrea...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>summarise given text\\nIn Q3 2024, the company ...</td>\n", "      <td>In Q3 2024, the company reported a 15% decline...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>summarise given text\\nThe logistics sector is ...</td>\n", "      <td>The sector is set for major expansion due to r...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>summarise given text\\nIn 2022, North America e...</td>\n", "      <td>A significant economic boost was observed due ...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>summarise given text\\nIn 2022, the company exp...</td>\n", "      <td>The company's financial success was significan...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>summarise given text\\nThe company is preparing...</td>\n", "      <td>The company is planning to address a projected...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>summarise given text\\nThe European market is p...</td>\n", "      <td>The European market's projected 8% rise in 202...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>summarise given text\\nThe logistics sector in ...</td>\n", "      <td>The logistics sector in Latin America is proje...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>summarise given text\\nIn North America, compan...</td>\n", "      <td>In North America, companies report a 5% decrea...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>summarise given text\\nIn 2023, the company exp...</td>\n", "      <td>In 2023, the company faced a 5% revenue decrea...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>summarise given text\\nIn Q3 2024, the company ...</td>\n", "      <td>In Q3 2024, the company reported an 8% rise in...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>summarise given text\\nThe European market expe...</td>\n", "      <td>The European market's 5% decrease in Q3 2024 h...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>summarise given text\\nIn 2022, Sales in North ...</td>\n", "      <td>A remarkable increase was achieved through str...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>summarise given text\\nThe logistics sector exp...</td>\n", "      <td>The logistics sector underwent a major transfo...</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                           user_input  \\\n", "0   summarise given text\\nThe Q2 earnings report r...   \n", "1   summarise given text\\nIn 2023, North American ...   \n", "2   summarise given text\\nIn 2022, European expans...   \n", "3   summarise given text\\nSupply chain challenges ...   \n", "4   summarise given text\\nIn Q2 2023, the company ...   \n", "5   summarise given text\\nIn 2023, marketing campa...   \n", "6   summarise given text\\nThe company's internatio...   \n", "7   summarise given text\\nIn 2024, companies are i...   \n", "8   summarise given text\\nIn 2023, logistics inves...   \n", "9   summarise given text\\nIn 2023, the company exp...   \n", "10  summarise given text\\nThe company reported a 5...   \n", "11  summarise given text\\nThe company reported a s...   \n", "12  summarise given text\\nThe global market has ex...   \n", "13  summarise given text\\nThe logistics industry i...   \n", "14  summarise given text\\nThe company reported an ...   \n", "15  summarise given text\\nIn 2022, the Asian marke...   \n", "16  summarise given text\\nThe global market has wi...   \n", "17  summarise given text\\nThe company reported a 1...   \n", "18  summarise given text\\nThe company's revenue sa...   \n", "19  summarise given text\\nThe Marketing team is st...   \n", "20  summarise given text\\nIn 2023, the global mark...   \n", "21  summarise given text\\nIn 2022, there was an 8%...   \n", "22  summarise given text\\nIn 2022, the global mark...   \n", "23  summarise given text\\nIn 2022, the European ma...   \n", "24  summarise given text\\nIn 2022, companies opera...   \n", "25  summarise given text\\nIn 2023, the European ma...   \n", "26  summarise given text\\nThe global market is poi...   \n", "27  summarise given text\\nThe company reported a s...   \n", "28  summarise given text\\nThe logistics industry i...   \n", "29  summarise given text\\nThe company is facing a ...   \n", "30  summarise given text\\nThe company is projectin...   \n", "31  summarise given text\\nIn 2023, the Asian marke...   \n", "32  summarise given text\\nThe global market has wi...   \n", "33  summarise given text\\nIn 2022, there was an 8%...   \n", "34  summarise given text\\nIn 2023, companies opera...   \n", "35  summarise given text\\nSales in Latin America e...   \n", "36  summarise given text\\nIn 2022, the company exp...   \n", "37  summarise given text\\nIn Q3 2024, the company ...   \n", "38  summarise given text\\nThe logistics sector is ...   \n", "39  summarise given text\\nIn 2022, North America e...   \n", "40  summarise given text\\nIn 2022, the company exp...   \n", "41  summarise given text\\nThe company is preparing...   \n", "42  summarise given text\\nThe European market is p...   \n", "43  summarise given text\\nThe logistics sector in ...   \n", "44  summarise given text\\nIn North America, compan...   \n", "45  summarise given text\\nIn 2023, the company exp...   \n", "46  summarise given text\\nIn Q3 2024, the company ...   \n", "47  summarise given text\\nThe European market expe...   \n", "48  summarise given text\\nIn 2022, Sales in North ...   \n", "49  summarise given text\\nThe logistics sector exp...   \n", "\n", "                                             response  summary_accuracy  \n", "0   The Q2 earnings report showed a 15% revenue in...                 1  \n", "1   Companies are strategizing to adapt to market ...                 1  \n", "2   Many companies experienced a notable 15% growt...                 1  \n", "3   Supply chain challenges in North America, caus...                 1  \n", "4   The company experienced a notable increase in ...                 1  \n", "5   In 2023, marketing campaigns in North America ...                 1  \n", "6   The company's international expansion strategy...                 1  \n", "7   Companies are using data analytics to customiz...                 1  \n", "8   Driven by technological and infrastructural ad...                 1  \n", "9   The company faced challenges due to competitio...                 1  \n", "10  The company faced challenges in the European m...                 1  \n", "11  The company's significant profit in Q3 2024, d...                 1  \n", "12  The recent downturn has raised concerns among ...                 1  \n", "13  The industry is expected to grow by 20% in 202...                 1  \n", "14  The company experienced an 8% increase in Q3 2...                 1  \n", "15  In 2022, the Asian market experienced a signif...                 1  \n", "16  The global market experienced a 10% increase i...                 1  \n", "17  The company experienced significant growth due...                 1  \n", "18  The company's financial success was significan...                 1  \n", "19  The team is strategizing to address the challe...                 1  \n", "20  In 2023, the global market saw a 5% sales decr...                 1  \n", "21  Economic factors led to increased expenses, pr...                 1  \n", "22  In 2022, a remarkable 20% growth significantly...                 1  \n", "23  In 2022, the European market experienced a 5% ...                 1  \n", "24  In 2022, companies in Latin America faced a 15...                 1  \n", "25  In 2023, the European market experienced a 15%...                 1  \n", "26  A significant shift is expected with a 20% gro...                 1  \n", "27  The company's 8% rise in 2022 was driven by ex...                 1  \n", "28  The logistics industry is expected to face a 5...                 1  \n", "29  The company is anticipating a difficult year d...                 1  \n", "30  The company's growth is fueled by strategic in...                 1  \n", "31  In 2023, the Asian market experienced a 10% in...                 1  \n", "32  The global market experienced an 8% rise in Q1...                 1  \n", "33  Expenses increased across various sectors in L...                 1  \n", "34  In 2023, companies in Latin America faced a 15...                 1  \n", "35  Sales in Latin America saw a remarkable 20% gr...                 1  \n", "36  In 2022, the company faced a 5% revenue decrea...                 1  \n", "37  In Q3 2024, the company reported a 15% decline...                 1  \n", "38  The sector is set for major expansion due to r...                 1  \n", "39  A significant economic boost was observed due ...                 1  \n", "40  The company's financial success was significan...                 1  \n", "41  The company is planning to address a projected...                 1  \n", "42  The European market's projected 8% rise in 202...                 1  \n", "43  The logistics sector in Latin America is proje...                 1  \n", "44  In North America, companies report a 5% decrea...                 1  \n", "45  In 2023, the company faced a 5% revenue decrea...                 1  \n", "46  In Q3 2024, the company reported an 8% rise in...                 1  \n", "47  The European market's 5% decrease in Q3 2024 h...                 1  \n", "48  A remarkable increase was achieved through str...                 1  \n", "49  The logistics sector underwent a major transfo...                 1  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["results.to_pandas()"]}], "metadata": {"kernelspec": {"display_name": "pytorch_is", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}