# Jina AI 嵌入模型 - RAGAS 自定义实现

这是一个为 RAGAS 框架定制的 Jina AI 嵌入模型实现，支持使用 Jina AI 的 API 服务进行文本嵌入。

## 🌟 特性

- ✅ **完全兼容 RAGAS**: 继承自 `BaseRagasEmbeddings`，无缝集成
- ✅ **同步/异步支持**: 支持同步和异步操作
- ✅ **批处理优化**: 自动分批处理大量文本，提高效率
- ✅ **错误重试机制**: 内置指数退避重试策略
- ✅ **灵活配置**: 支持多种配置选项
- ✅ **详细日志**: 完整的操作日志记录
- ✅ **类型提示**: 完整的 Python 类型注解

## 📦 安装依赖

```bash
pip install -r requirements_jina.txt
```

或者手动安装核心依赖：

```bash
pip install httpx ragas
```

## 🚀 快速开始

### 1. 获取 Jina AI API 密钥

访问 [Jina AI](https://jina.ai/) 获取您的 API 密钥。

### 2. 基本使用

```python
from jina_embeddings import create_jina_embeddings

# 创建嵌入实例
embeddings = create_jina_embeddings(
    api_key="your-jina-api-key",
    model_name="jina-embeddings-v3"
)

# 嵌入文本
texts = ["人工智能正在改变世界", "机器学习是AI的重要分支"]
vectors = embeddings.embed_documents(texts)

print(f"获得 {len(vectors)} 个嵌入向量，维度: {len(vectors[0])}")
```

### 3. 与 RAGAS 指标集成

```python
from ragas.metrics import SemanticSimilarity
from ragas import SingleTurnSample

# 创建使用自定义嵌入的指标
metric = SemanticSimilarity(embeddings=embeddings)

# 创建测试样本
sample = SingleTurnSample(
    user_input="什么是机器学习？",
    response="机器学习是人工智能的一个分支...",
    reference="机器学习是AI的子领域..."
)

# 计算相似度
score = metric.single_turn_score(sample)
print(f"语义相似度: {score}")
```

## ⚙️ 配置选项

### 基本配置

```python
from jina_embeddings import JinaEmbeddings

embeddings = JinaEmbeddings(
    api_key="your-api-key",
    model_name="jina-embeddings-v3",    # 模型名称
    batch_size=50,                      # 批处理大小
    timeout=30,                         # 请求超时（秒）
    max_retries=3,                      # 最大重试次数
    base_url="https://api.jina.ai/v1/embeddings"  # API 端点
)
```

### 环境变量配置

```bash
# 设置环境变量
export JINA_API_KEY="your-jina-api-key"
```

```python
import os
from jina_embeddings import create_jina_embeddings

# 从环境变量读取
embeddings = create_jina_embeddings(
    api_key=os.getenv("JINA_API_KEY")
)
```

## 🔧 高级用法

### 异步操作

```python
import asyncio

async def async_embedding_example():
    embeddings = create_jina_embeddings(api_key="your-key")
    
    # 异步嵌入
    texts = ["文本1", "文本2", "文本3"]
    vectors = await embeddings.aembed_documents(texts)
    
    # 异步查询嵌入
    query_vector = await embeddings.aembed_query("查询文本")
    
    return vectors, query_vector

# 运行异步函数
vectors, query_vector = asyncio.run(async_embedding_example())
```

### 批处理大量文本

```python
# 处理大量文本时，会自动分批
large_text_list = [f"文本 {i}" for i in range(1000)]
embeddings = create_jina_embeddings(
    api_key="your-key",
    batch_size=100  # 每批处理100个文本
)

vectors = embeddings.embed_documents(large_text_list)
print(f"处理了 {len(vectors)} 个文本")
```

### 自定义运行配置

```python
from ragas.run_config import RunConfig

# 创建自定义运行配置
run_config = RunConfig(
    timeout=60,
    max_workers=4
)

embeddings = JinaEmbeddings(
    api_key="your-key",
    run_config=run_config
)
```

## 📊 性能优化建议

### 1. 批处理大小调优

```python
# 根据您的网络和API限制调整
embeddings = create_jina_embeddings(
    api_key="your-key",
    batch_size=50  # 较小的批次更稳定，较大的批次更高效
)
```

### 2. 超时和重试设置

```python
embeddings = create_jina_embeddings(
    api_key="your-key",
    timeout=60,      # 增加超时时间处理大批次
    max_retries=5    # 增加重试次数提高成功率
)
```

### 3. 异步处理提高并发

```python
import asyncio

async def process_multiple_batches():
    embeddings = create_jina_embeddings(api_key="your-key")
    
    # 并发处理多个批次
    batch1 = ["文本1", "文本2"]
    batch2 = ["文本3", "文本4"]
    
    results = await asyncio.gather(
        embeddings.aembed_documents(batch1),
        embeddings.aembed_documents(batch2)
    )
    
    return results
```

## 🐛 故障排除

### 常见问题

1. **API 密钥错误**
   ```
   ValueError: API 密钥不能为空
   ```
   解决：确保设置了正确的 API 密钥

2. **网络超时**
   ```
   httpx.TimeoutException
   ```
   解决：增加 `timeout` 参数值

3. **批处理过大**
   ```
   HTTP 413 Payload Too Large
   ```
   解决：减小 `batch_size` 参数

### 调试模式

```python
import logging

# 启用详细日志
logging.basicConfig(level=logging.INFO)

embeddings = create_jina_embeddings(api_key="your-key")
# 现在会显示详细的操作日志
```

## 🧪 运行测试

```bash
# 运行示例测试
python jina_embeddings_example.py

# 设置API密钥后运行
export JINA_API_KEY="your-key"
python jina_embeddings_example.py
```

## 📝 API 参考

### JinaEmbeddings 类

#### 构造函数参数

- `api_key` (str): Jina AI API 密钥
- `model_name` (str): 模型名称，默认 "jina-embeddings-v3"
- `base_url` (str): API 基础 URL
- `batch_size` (int): 批处理大小，默认 100
- `timeout` (int): 请求超时时间，默认 30 秒
- `max_retries` (int): 最大重试次数，默认 3

#### 主要方法

- `embed_documents(texts: List[str]) -> List[List[float]]`: 同步嵌入多个文档
- `embed_query(text: str) -> List[float]`: 同步嵌入单个查询
- `aembed_documents(texts: List[str]) -> List[List[float]]`: 异步嵌入多个文档
- `aembed_query(text: str) -> List[float]`: 异步嵌入单个查询

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个实现！

## 📄 许可证

本项目遵循 MIT 许可证。
