# Jina AI 嵌入模型 - RAGAS 自定义实现

这是一个为 RAGAS 框架定制的 Jina AI 嵌入模型实现，支持使用 Jina AI 的 API 服务进行文本嵌入。

## 📁 项目结构

```
ragas/
├── jina_embeddings.py          # 核心嵌入实现
├── pytest.ini                  # pytest 配置
├── pyproject.toml              # 项目配置
├── README.md                   # 项目说明（本文件）
├── docs/                       # 📚 文档目录
│   ├── README_jina_embeddings.md  # 详细使用文档
│   └── requirements_jina.txt       # 依赖包列表
├── tests/                      # 🧪 测试目录
│   ├── __init__.py
│   ├── test_jina_embeddings.py     # 单元测试
│   ├── test_jina_real.py           # 真实 API 测试
│   ├── test_jina_api_direct.py     # 直接 API 测试
│   └── simple_integration_test.py  # 简单集成测试
└── examples/                   # 📖 示例目录
    ├── jina_embeddings_example.py  # 基本使用示例
    ├── demo_with_jina.py           # 完整演示
    └── final_demo.py               # 最终演示
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r docs/requirements_jina.txt
```

### 2. 基本使用

```python
from jina_embeddings import create_jina_embeddings

# 创建嵌入实例
embeddings = create_jina_embeddings(
    api_key="your-jina-api-key",
    task="retrieval.query"
)

# 嵌入文本
texts = ["人工智能正在改变世界", "机器学习是AI的重要分支"]
vectors = embeddings.embed_documents(texts)
```

### 3. 与 RAGAS 集成

```python
from ragas.metrics import SemanticSimilarity

# 创建指标
metric = SemanticSimilarity(embeddings=embeddings)

# 评估样本
score = metric.single_turn_score(sample)
```

## 🧪 运行测试

### 单元测试
```bash
cd tests
python -m pytest test_jina_embeddings.py -v
```

### 真实 API 测试
```bash
cd tests
python test_jina_real.py
```

### 集成测试
```bash
cd tests
python simple_integration_test.py
```

## 📖 运行示例

### 基本示例
```bash
cd examples
python jina_embeddings_example.py
```

### 完整演示
```bash
cd examples
python final_demo.py
```

## 📚 详细文档

请查看 [`docs/README_jina_embeddings.md`](docs/README_jina_embeddings.md) 获取详细的使用文档，包括：

- 完整的 API 参考
- 配置选项说明
- 性能优化建议
- 故障排除指南
- 高级用法示例

## 🌟 主要特性

- ✅ **完全兼容 RAGAS**: 继承自 `BaseRagasEmbeddings`
- ✅ **同步/异步支持**: 支持同步和异步操作
- ✅ **批处理优化**: 自动分批处理大量文本
- ✅ **错误重试机制**: 内置指数退避重试策略
- ✅ **灵活配置**: 支持多种配置选项
- ✅ **详细日志**: 完整的操作日志记录

## ⚙️ 环境要求

- Python 3.7+
- httpx >= 0.24.0
- ragas >= 0.2.0
- 有效的 Jina AI API 密钥

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个实现！

## 📄 许可证

本项目遵循 MIT 许可证。
