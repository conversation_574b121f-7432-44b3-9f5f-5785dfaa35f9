# Part0

import lazyllm
from lazyllm.module import OnlineEmbeddingModuleBase
from typing import Dict, List, Union

# 自定义 Jina AI 嵌入模块
class JinaEmbeddingModule(OnlineEmbeddingModuleBase):
    """Jina AI 嵌入模块 - 符合 OpenAI 格式"""

    def __init__(self, api_key: str, model_name: str = "jina-embeddings-v3", task: str = "text-matching"):
        # Jina AI 的 API 端点
        embed_url = "https://api.jina.ai/v1/embeddings"

        super().__init__(
            model_series="openai",  # 使用 openai 格式
            embed_url=embed_url,
            api_key=api_key,
            embed_model_name=model_name
        )
        self.task = task

    def _encapsulated_data(self, text: str, **kwargs) -> Dict:
        """封装请求数据为 OpenAI/Jina AI 格式"""
        # 如果是单个文本，转换为列表
        if isinstance(text, str):
            input_texts = [text]
        else:
            input_texts = text if isinstance(text, list) else [text]

        json_data = {
            "model": self._embed_model_name,
            "input": input_texts,
            "task": self.task  # Jina AI 特有的参数
        }

        # 添加其他参数
        if len(kwargs) > 0:
            json_data.update(kwargs)

        return json_data

    def _parse_response(self, response: Union[List, Dict]) -> Union[List[List[float]], List[float]]:
        """解析 OpenAI/Jina AI 格式的响应"""
        if isinstance(response, dict) and "data" in response:
            # OpenAI/Jina AI 返回格式: {"data": [{"embedding": [...]}, ...]}
            embeddings = [item["embedding"] for item in response["data"]]
            return embeddings[0] if len(embeddings) == 1 else embeddings
        else:
            raise ValueError(f"无法解析响应格式: {response}")

# 创建 Jina AI 嵌入模块实例
jina_embedding = JinaEmbeddingModule(
    api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
    model_name="jina-embeddings-v3",
    task="text-matching"
)

# Part1

documents = lazyllm.Document(dataset_path="/path/to/your/doc/dir",
                             embed=jina_embedding,
                             manager=False)

# Part2

retriever = lazyllm.Retriever(doc=documents,
                              group_name="CoarseChunk",
                              similarity="bm25_chinese",
                              topk=3)

# Part3


llm = lazyllm.OnlineChatModule(
    source="openai",
    model="ep-20250822223253-s98w6",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    stream=True, return_trace=True
    ) 

# Part4

prompt = '你将扮演一个人工智能问答助手的角色，完成一项对话任务。在这个任务中，你需要根据给定的上下文以及问题，给出你的回答。'
llm.prompt(lazyllm.ChatPrompter(instruction=prompt, extra_keys=['context_str']))

# Part5

query = input("query(enter 'quit' to exit): ")
if query == "quit":
    exit(0)

# Part6

doc_node_list = retriever(query=query)

res = llm({
    "query": query,
    "context_str": "".join([node.get_content() for node in doc_node_list]),
})

# Part7

print(f"answer: {res}")