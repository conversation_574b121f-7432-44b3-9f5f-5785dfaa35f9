#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Jina AI 嵌入 API 调用
"""

import lazyllm
from lazyllm.module import OnlineEmbeddingModuleBase
from typing import Dict, List, Union

print("🚀 测试 Jina AI 嵌入 API 调用")
print("=" * 50)

# 自定义 Jina AI 嵌入模块
class JinaEmbeddingModule(OnlineEmbeddingModuleBase):
    """Jina AI 嵌入模块 - 符合 OpenAI 格式"""
    
    def __init__(self, api_key: str, model_name: str = "jina-embeddings-v3", task: str = "text-matching"):
        # Jina AI 的 API 端点
        embed_url = "https://api.jina.ai/v1/embeddings"
        
        super().__init__(
            model_series="openai",  # 使用 openai 格式
            embed_url=embed_url,
            api_key=api_key,
            embed_model_name=model_name
        )
        self.task = task
    
    def _encapsulated_data(self, text: str, **kwargs) -> Dict:
        """封装请求数据为 OpenAI/Jina AI 格式"""
        # 如果是单个文本，转换为列表
        if isinstance(text, str):
            input_texts = [text]
        else:
            input_texts = text if isinstance(text, list) else [text]
            
        json_data = {
            "model": self._embed_model_name,
            "input": input_texts,
            "task": self.task  # Jina AI 特有的参数
        }
        
        # 添加其他参数
        if len(kwargs) > 0:
            json_data.update(kwargs)
            
        print(f"🔍 请求数据: {json_data}")
        return json_data
    
    def _parse_response(self, response: Union[List, Dict]) -> Union[List[List[float]], List[float]]:
        """解析 OpenAI/Jina AI 格式的响应"""
        print(f"📥 收到响应: {type(response)}")
        if isinstance(response, dict):
            print(f"📋 响应键: {list(response.keys())}")
            
        if isinstance(response, dict) and "data" in response:
            # OpenAI/Jina AI 返回格式: {"data": [{"embedding": [...]}, ...]}
            embeddings = [item["embedding"] for item in response["data"]]
            result = embeddings[0] if len(embeddings) == 1 else embeddings
            print(f"✅ 解析成功，嵌入维度: {len(result) if isinstance(result, list) else 'N/A'}")
            return result
        else:
            print(f"❌ 无法解析响应格式: {response}")
            raise ValueError(f"无法解析响应格式: {response}")

def test_embedding():
    """测试嵌入功能"""
    try:
        print("🔧 创建 Jina AI 嵌入模块...")
        jina_embedding = JinaEmbeddingModule(
            api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
            model_name="jina-embeddings-v3",
            task="text-matching"
        )
        print("✅ 模块创建成功")
        
        # 测试文本
        test_text = "这是一个测试文本，用于验证 Jina AI 嵌入功能。"
        print(f"📝 测试文本: {test_text}")
        
        print("🌐 正在调用 Jina AI API...")
        embedding = jina_embedding(test_text)
        
        print(f"🎉 嵌入成功!")
        print(f"   维度: {len(embedding)}")
        print(f"   前5个值: {embedding[:5]}")
        print(f"   数据类型: {type(embedding)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 嵌入失败: {e}")
        import traceback
        print("📋 详细错误信息:")
        traceback.print_exc()
        return False

def test_multiple_texts():
    """测试多个文本"""
    try:
        print("\n🔧 测试多个文本...")
        jina_embedding = JinaEmbeddingModule(
            api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
            model_name="jina-embeddings-v3",
            task="text-matching"
        )
        
        test_texts = [
            "人工智能是计算机科学的一个分支。",
            "机器学习是人工智能的重要组成部分。",
            "深度学习使用神经网络进行学习。"
        ]
        
        embeddings = []
        for i, text in enumerate(test_texts, 1):
            print(f"📝 处理文本 {i}: {text}")
            embedding = jina_embedding(text)
            embeddings.append(embedding)
            print(f"✅ 文本 {i} 嵌入成功，维度: {len(embedding)}")
        
        print(f"🎉 所有文本处理完成! 共 {len(embeddings)} 个嵌入向量")
        
        # 计算相似度
        if len(embeddings) >= 2:
            import numpy as np
            vec1 = np.array(embeddings[0])
            vec2 = np.array(embeddings[1])
            similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
            print(f"📊 文本1和文本2的相似度: {similarity:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多文本测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试...")
    
    # 测试单个文本
    success1 = test_embedding()
    
    if success1:
        # 测试多个文本
        success2 = test_multiple_texts()
        
        if success2:
            print("\n🎉 所有测试通过!")
            print("💡 Jina AI 嵌入模块工作正常，可以在 RAG 系统中使用")
        else:
            print("\n⚠️  多文本测试失败，但单文本测试成功")
    else:
        print("\n❌ 基础测试失败，请检查:")
        print("   1. 网络连接")
        print("   2. API Key 是否有效")
        print("   3. Jina AI 服务状态")

if __name__ == "__main__":
    main()
