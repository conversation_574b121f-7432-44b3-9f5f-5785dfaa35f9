#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LazyLLM + Jina AI 嵌入模块演示
使用预设查询，适合非交互式环境
"""

import lazyllm
from lazyllm.module import OnlineEmbeddingModuleBase
from typing import Dict, List, Union
import os

print("🚀 LazyLLM + Jina AI RAG 系统演示")
print("=" * 60)

# 自定义 Jina AI 嵌入模块
class JinaEmbeddingModule(OnlineEmbeddingModuleBase):
    """Jina AI 嵌入模块 - 符合 OpenAI 格式"""
    
    def __init__(self, api_key: str, model_name: str = "jina-embeddings-v3", task: str = "text-matching"):
        # Jina AI 的 API 端点
        embed_url = "https://api.jina.ai/v1/embeddings"
        
        super().__init__(
            model_series="openai",  # 使用 openai 格式
            embed_url=embed_url,
            api_key=api_key,
            embed_model_name=model_name
        )
        self.task = task
    
    def _encapsulated_data(self, text: str, **kwargs) -> Dict:
        """封装请求数据为 OpenAI/Jina AI 格式"""
        # 如果是单个文本，转换为列表
        if isinstance(text, str):
            input_texts = [text]
        else:
            input_texts = text if isinstance(text, list) else [text]
            
        json_data = {
            "model": self._embed_model_name,
            "input": input_texts,
            "task": self.task  # Jina AI 特有的参数
        }
        
        # 添加其他参数
        if len(kwargs) > 0:
            json_data.update(kwargs)
            
        return json_data
    
    def _parse_response(self, response: Union[List, Dict]) -> Union[List[List[float]], List[float]]:
        """解析 OpenAI/Jina AI 格式的响应"""
        if isinstance(response, dict) and "data" in response:
            # OpenAI/Jina AI 返回格式: {"data": [{"embedding": [...]}, ...]}
            embeddings = [item["embedding"] for item in response["data"]]
            return embeddings[0] if len(embeddings) == 1 else embeddings
        else:
            raise ValueError(f"无法解析响应格式: {response}")

def create_sample_documents():
    """创建示例文档用于测试"""
    sample_docs_dir = "./sample_docs"
    
    if not os.path.exists(sample_docs_dir):
        os.makedirs(sample_docs_dir)
        print(f"📁 创建示例文档目录: {sample_docs_dir}")
        
        # 创建示例文档
        sample_texts = {
            "ai_basics.txt": """
人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

人工智能的主要应用领域包括：
1. 机器学习：让计算机能够自动学习和改进
2. 自然语言处理：使计算机能够理解和生成人类语言
3. 计算机视觉：让计算机能够识别和理解图像
4. 机器人技术：创造能够执行复杂任务的自主机器

机器学习是人工智能的一个重要分支，它使用算法来解析数据、学习数据中的模式，然后对新数据做出预测或决策。
            """,
            "deep_learning.txt": """
深度学习是机器学习的一个子集，它使用多层神经网络来学习数据的复杂模式。

深度学习的特点：
- 使用多层神经网络结构
- 能够自动提取特征
- 在图像识别、语音识别等领域表现优异
- 需要大量的训练数据

常见的深度学习模型包括：
1. 卷积神经网络（CNN）：主要用于图像处理
2. 循环神经网络（RNN）：适合处理序列数据
3. 长短期记忆网络（LSTM）：解决RNN的长期依赖问题
4. 变换器（Transformer）：在自然语言处理中表现出色
            """,
            "nlp_intro.txt": """
自然语言处理（Natural Language Processing，NLP）是人工智能的一个重要分支，专注于让计算机理解、解释和生成人类语言。

NLP的主要任务包括：
- 文本分类：将文本分配到预定义的类别
- 情感分析：判断文本的情感倾向
- 命名实体识别：识别文本中的人名、地名、机构名等
- 机器翻译：将一种语言翻译成另一种语言
- 问答系统：根据问题提供准确的答案

现代NLP技术大量使用深度学习方法，特别是基于Transformer架构的大型语言模型，如GPT、BERT等。
            """
        }
        
        for filename, content in sample_texts.items():
            with open(os.path.join(sample_docs_dir, filename), 'w', encoding='utf-8') as f:
                f.write(content.strip())
        
        print(f"✅ 创建了 {len(sample_texts)} 个示例文档")
    else:
        print(f"📁 使用现有文档目录: {sample_docs_dir}")
    
    return sample_docs_dir

def main():
    """主演示函数"""
    try:
        print("🔧 步骤 1: 创建 Jina AI 嵌入模块...")
        jina_embedding = JinaEmbeddingModule(
            api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
            model_name="jina-embeddings-v3",
            task="text-matching"
        )
        print("✅ Jina AI 嵌入模块创建成功")
        
        print("\n🔧 步骤 2: 准备文档数据...")
        doc_path = create_sample_documents()
        
        print("\n🔧 步骤 3: 创建文档对象...")
        documents = lazyllm.Document(
            dataset_path=doc_path,
            embed=jina_embedding,
            manager=False
        )
        print("✅ 文档对象创建成功")
        
        print("\n🔧 步骤 4: 创建检索器...")
        retriever = lazyllm.Retriever(
            doc=documents,
            group_name="CoarseChunk",
            similarity="bm25_chinese",
            topk=3
        )
        print("✅ 检索器创建成功")
        
        print("\n🔧 步骤 5: 创建语言模型...")
        llm = lazyllm.OnlineChatModule(
            source="openai",
            model="ep-20250822223253-s98w6",
            base_url="https://ark.cn-beijing.volces.com/api/v3/",
            api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
            stream=True, return_trace=True
        )
        
        prompt = '你将扮演一个人工智能问答助手的角色，完成一项对话任务。在这个任务中，你需要根据给定的上下文以及问题，给出你的回答。'
        llm.prompt(lazyllm.ChatPrompter(instruction=prompt, extra_keys=['context_str']))
        print("✅ 语言模型创建成功")
        
        print("\n🎯 步骤 6: 开始问答演示...")
        
        # 预设的测试查询
        test_queries = [
            "什么是人工智能？",
            "深度学习有什么特点？",
            "自然语言处理的主要任务有哪些？",
            "机器学习和深度学习有什么关系？"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{'='*50}")
            print(f"🔍 查询 {i}: {query}")
            
            try:
                # 检索相关文档
                print("📚 正在检索相关文档...")
                doc_node_list = retriever(query=query)
                print(f"✅ 找到 {len(doc_node_list)} 个相关文档片段")
                
                # 显示检索到的内容
                context_str = "".join([node.get_content() for node in doc_node_list])
                print(f"📄 检索到的内容长度: {len(context_str)} 字符")
                
                # 生成回答
                print("🤔 正在生成回答...")
                res = llm({
                    "query": query,
                    "context_str": context_str,
                })
                
                print(f"💬 回答: {res}")
                
            except Exception as e:
                print(f"❌ 处理查询时出错: {e}")
        
        print(f"\n{'='*60}")
        print("🎉 演示完成!")
        print("💡 总结:")
        print("   ✅ Jina AI 嵌入模块工作正常")
        print("   ✅ 文档检索功能正常")
        print("   ✅ 问答生成功能正常")
        print("   ✅ 完整的 RAG 系统运行成功")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        print("📋 详细错误信息:")
        traceback.print_exc()
        
        print("\n💡 可能的解决方案:")
        print("   1. 检查网络连接")
        print("   2. 验证 API Key 是否有效")
        print("   3. 确认所有依赖包已安装")

if __name__ == "__main__":
    main()
