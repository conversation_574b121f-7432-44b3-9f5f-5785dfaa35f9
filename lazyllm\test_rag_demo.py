#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的 RAG 演示脚本 - 适用于非交互式环境
"""

import lazyllm
from lazyllm.module import OnlineEmbeddingModuleBase
from typing import Dict, List, Union
import sys
import os

# 自定义 Jina AI 嵌入模块
class JinaEmbeddingModule(OnlineEmbeddingModuleBase):
    """Jina AI 嵌入模块 - 符合 OpenAI 格式"""
    
    def __init__(self, api_key: str, model_name: str = "jina-embeddings-v3", task: str = "text-matching"):
        # Jina AI 的 API 端点
        embed_url = "https://api.jina.ai/v1/embeddings"
        
        super().__init__(
            model_series="openai",  # 使用 openai 格式
            embed_url=embed_url,
            api_key=api_key,
            embed_model_name=model_name
        )
        self.task = task
    
    def _encapsulated_data(self, text: str, **kwargs) -> Dict:
        """封装请求数据为 OpenAI/Jina AI 格式"""
        # 如果是单个文本，转换为列表
        if isinstance(text, str):
            input_texts = [text]
        else:
            input_texts = text if isinstance(text, list) else [text]
            
        json_data = {
            "model": self._embed_model_name,
            "input": input_texts,
            "task": self.task  # Jina AI 特有的参数
        }
        
        # 添加其他参数
        if len(kwargs) > 0:
            json_data.update(kwargs)
            
        return json_data
    
    def _parse_response(self, response: Union[List, Dict]) -> Union[List[List[float]], List[float]]:
        """解析 OpenAI/Jina AI 格式的响应"""
        if isinstance(response, dict) and "data" in response:
            # OpenAI/Jina AI 返回格式: {"data": [{"embedding": [...]}, ...]}
            embeddings = [item["embedding"] for item in response["data"]]
            return embeddings[0] if len(embeddings) == 1 else embeddings
        else:
            raise ValueError(f"无法解析响应格式: {response}")

def test_jina_embedding_only():
    """仅测试 Jina AI 嵌入功能"""
    print("🧪 测试 Jina AI 嵌入模块")
    print("=" * 50)
    
    try:
        # 创建 Jina AI 嵌入模块实例
        jina_embedding = JinaEmbeddingModule(
            api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
            model_name="jina-embeddings-v3",
            task="text-matching"
        )
        
        print("✅ Jina AI 嵌入模块创建成功")
        
        # 测试文本
        test_text = "这是一个测试文本，用于验证 Jina AI 嵌入功能。"
        print(f"📝 测试文本: {test_text}")
        
        # 获取嵌入向量
        print("🔄 正在获取嵌入向量...")
        embedding = jina_embedding(test_text)
        
        print(f"✅ 嵌入成功!")
        print(f"   维度: {len(embedding)}")
        print(f"   前5个值: {embedding[:5]}")
        print(f"   数据类型: {type(embedding)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 嵌入测试失败: {e}")
        print("💡 可能的原因:")
        print("   1. 网络连接问题")
        print("   2. API Key 无效")
        print("   3. Jina AI 服务不可用")
        return False

def test_full_rag_system():
    """测试完整的 RAG 系统（需要文档）"""
    print("\n📚 测试完整 RAG 系统")
    print("=" * 50)
    
    try:
        # 创建 Jina AI 嵌入模块实例
        jina_embedding = JinaEmbeddingModule(
            api_key="jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR",
            model_name="jina-embeddings-v3",
            task="text-matching"
        )
        
        # 检查是否有文档路径
        doc_path = "/path/to/your/doc/dir"  # 默认路径
        
        # 尝试一些常见的文档路径
        possible_paths = [
            "./docs",
            "./data",
            "./documents",
            "../docs",
            "../data"
        ]
        
        actual_doc_path = None
        for path in possible_paths:
            if os.path.exists(path):
                actual_doc_path = path
                break
        
        if actual_doc_path:
            print(f"📁 找到文档路径: {actual_doc_path}")
            
            # 创建文档对象
            documents = lazyllm.Document(
                dataset_path=actual_doc_path,
                embed=jina_embedding,
                manager=False
            )
            
            # 创建检索器
            retriever = lazyllm.Retriever(
                doc=documents,
                group_name="CoarseChunk",
                similarity="bm25_chinese",
                topk=3
            )
            
            # 创建 LLM
            llm = lazyllm.OnlineChatModule(
                source="openai",
                model="ep-20250822223253-s98w6",
                base_url="https://ark.cn-beijing.volces.com/api/v3/",
                api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
                stream=True, return_trace=True
            )
            
            prompt = '你将扮演一个人工智能问答助手的角色，完成一项对话任务。在这个任务中，你需要根据给定的上下文以及问题，给出你的回答。'
            llm.prompt(lazyllm.ChatPrompter(instruction=prompt, extra_keys=['context_str']))
            
            # 测试查询
            test_query = "什么是人工智能？"
            print(f"🔍 测试查询: {test_query}")
            
            # 检索文档
            print("📚 正在检索相关文档...")
            doc_node_list = retriever(query=test_query)
            print(f"✅ 找到 {len(doc_node_list)} 个相关文档片段")
            
            # 生成回答
            print("🤔 正在生成回答...")
            res = llm({
                "query": test_query,
                "context_str": "".join([node.get_content() for node in doc_node_list]),
            })
            
            print(f"💬 回答: {res}")
            return True
            
        else:
            print("⚠️  未找到文档路径，跳过完整 RAG 测试")
            print("💡 要测试完整功能，请:")
            print("   1. 创建 docs/ 文件夹并放入文档")
            print("   2. 或修改代码中的文档路径")
            return False
            
    except Exception as e:
        print(f"❌ RAG 系统测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 LazyLLM + Jina AI 集成测试")
    print("=" * 60)
    
    # 测试 1: 仅测试嵌入功能
    embedding_success = test_jina_embedding_only()
    
    if embedding_success:
        print("\n🎉 嵌入功能测试通过!")
        
        # 测试 2: 完整 RAG 系统（如果有文档）
        rag_success = test_full_rag_system()
        
        if rag_success:
            print("\n🎉 完整 RAG 系统测试通过!")
        else:
            print("\n⚠️  完整 RAG 系统测试跳过（需要文档）")
    else:
        print("\n❌ 嵌入功能测试失败，请检查配置")
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"   嵌入功能: {'✅ 通过' if embedding_success else '❌ 失败'}")
    
    if embedding_success:
        print("💡 下一步:")
        print("   1. 准备文档数据")
        print("   2. 修改文档路径")
        print("   3. 运行完整的 RAG 系统")

if __name__ == "__main__":
    main()
